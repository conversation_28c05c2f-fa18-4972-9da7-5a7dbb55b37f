/**
 * EvenOut Firebase Functions
 * OTP Email Service using Gmail SMTP and NodeMailer
 */

const {setGlobalOptions} = require("firebase-functions");
const {onCall} = require("firebase-functions/v2/https");
const {onRequest} = require("firebase-functions/https");
const logger = require("firebase-functions/logger");
const admin = require("firebase-admin");
const nodemailer = require("nodemailer");
const crypto = require("crypto");

// Initialize Firebase Admin
admin.initializeApp();

// Set global options for cost control
setGlobalOptions({maxInstances: 10});

// Gmail SMTP configuration
const createTransporter = () => {
  // Hardcode the credentials temporarily for testing
  const gmailUser = "<EMAIL>";
  const gmailPassword = "eeasivsixwnyqlhq"; // This should be moved to environment variables

  logger.info("Creating transporter", {
    hasUser: !!gmailUser,
    hasPassword: !!gmailPassword,
    user: gmailUser,
  });

  return nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: gmailUser,
      pass: gmailPassword,
    },
  });
};

// Generate 6-digit OTP
const generateOTP = () => {
  return crypto.randomInt(100000, 999999).toString();
};

// Email template for OTP
const createOTPEmailTemplate = (otp, userName) => {
  return {
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>EvenOut - Email Verification</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #2D5A5A;
            background-color: #F5F1E8;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-top: 40px;
            margin-bottom: 40px;
          }
          .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #3AD29F, #2D5A5A);
            border-radius: 16px 16px 0 0;
            margin: -20px -20px 30px -20px;
          }
          .logo {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin: 0;
          }
          .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin: 5px 0 0 0;
          }
          .content {
            padding: 0 20px;
          }
          .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #2D5A5A;
            margin-bottom: 20px;
          }
          .message {
            font-size: 16px;
            color: #5A8A8A;
            margin-bottom: 30px;
            line-height: 1.6;
          }
          .otp-container {
            background: linear-gradient(135deg, #F0F8F5, #E8F5F0);
            border: 2px solid #3AD29F;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
          }
          .otp-label {
            font-size: 14px;
            color: #5A8A8A;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
          }
          .otp-code {
            font-size: 36px;
            font-weight: bold;
            color: #2D5A5A;
            letter-spacing: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
          }
          .otp-note {
            font-size: 12px;
            color: #5A8A8A;
            margin-top: 15px;
          }
          .warning {
            background-color: #FFF3E0;
            border-left: 4px solid #E6C068;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
          }
          .warning-text {
            font-size: 14px;
            color: #B8860B;
            margin: 0;
          }
          .footer {
            text-align: center;
            padding: 30px 0;
            border-top: 1px solid #E0E0E0;
            margin-top: 40px;
          }
          .footer-text {
            font-size: 12px;
            color: #5A8A8A;
            margin: 5px 0;
          }
          .app-info {
            background-color: #F8F8F8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }
          .app-info-title {
            font-size: 16px;
            font-weight: 600;
            color: #2D5A5A;
            margin-bottom: 10px;
          }
          .app-info-text {
            font-size: 14px;
            color: #5A8A8A;
            margin: 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 class="logo">EvenOut</h1>
            <p class="subtitle">Community Exchange Platform</p>
          </div>

          <div class="content">
            <h2 class="greeting">Hello ${userName || "there"}! 👋</h2>

            <p class="message">
              Welcome to EvenOut! We're excited to have you join our community of people who believe in sharing and exchanging goods sustainably.
            </p>

            <p class="message">
              To complete your email verification, please use the following One-Time Password (OTP):
            </p>

            <div class="otp-container">
              <div class="otp-label">Your Verification Code</div>
              <div class="otp-code">${otp}</div>
              <div class="otp-note">This code expires in 5 minutes</div>
            </div>

            <div class="warning">
              <p class="warning-text">
                <strong>Security Notice:</strong> Never share this code with anyone. EvenOut will never ask for your OTP via phone or email.
              </p>
            </div>

            <div class="app-info">
              <div class="app-info-title">What's Next?</div>
              <p class="app-info-text">
                Once verified, you'll be able to list items, browse your community's offerings, and start making meaningful exchanges with your neighbors.
              </p>
            </div>

            <p class="message">
              If you didn't request this verification, please ignore this email or contact our support team.
            </p>
          </div>

          <div class="footer">
            <p class="footer-text">© 2025 EvenOut - Building Sustainable Communities</p>
            <p class="footer-text">This is an automated message, please do not reply to this email.</p>
            <p class="footer-text">Need help? Contact <NAME_EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Hello ${userName || "there"}!

Welcome to EvenOut! We're excited to have you join our community.

Your email verification code is: ${otp}

This code expires in 5 minutes.

Security Notice: Never share this code with anyone. EvenOut will never ask for your OTP via phone or email.

If you didn't request this verification, please ignore this email.

© 2024 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
    `,
  };
};

// Welcome email template
const createWelcomeEmailTemplate = (userName) => {
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #3AD29F, #2D5A5A); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">Welcome to EvenOut! 🌱</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">Building Sustainable Communities Together</p>
      </div>

      <div style="padding: 30px 20px;">
        <h2 style="color: #2D5A5A;">Hello ${userName || "there"}! 👋</h2>

        <p style="color: #5A8A8A; line-height: 1.6;">
          Welcome to EvenOut! We're thrilled to have you join our community of people who believe in sharing, exchanging, and building a more sustainable future together.
        </p>

        <div style="background: #F0F8F5; border-left: 4px solid #3AD29F; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
          <h3 style="color: #2D5A5A; margin: 0 0 10px 0;">🎯 What's Next?</h3>
          <ul style="color: #5A8A8A; margin: 0; padding-left: 20px;">
            <li>Complete your profile verification</li>
            <li>Start listing items you'd like to share or exchange</li>
            <li>Explore what your neighbors are offering</li>
            <li>Connect with your local community</li>
          </ul>
        </div>

        <div style="background: #FFF3E0; border-left: 4px solid #E6C068; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
          <h3 style="color: #B8860B; margin: 0 0 10px 0;">🔒 Security First</h3>
          <p style="color: #B8860B; margin: 0; font-size: 14px;">
            We recommend completing your email and document verification to build trust within the community and unlock all features.
          </p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <div style="background: #3AD29F; color: white; padding: 15px 30px; border-radius: 25px; display: inline-block; font-weight: bold;">
            Ready to make a difference? Let's get started! 🚀
          </div>
        </div>

        <p style="color: #5A8A8A; line-height: 1.6;">
          If you have any questions or need help getting started, don't hesitate to reach out to our support team.
        </p>
      </div>

      <div style="text-align: center; padding: 20px; border-top: 1px solid #E0E0E0; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
Welcome to EvenOut! 🌱

Hello ${userName || "there"}!

We're thrilled to have you join our community of people who believe in sharing, exchanging, and building a more sustainable future together.

What's Next?
- Complete your profile verification
- Start listing items you'd like to share or exchange
- Explore what your neighbors are offering
- Connect with your local community

Security First:
We recommend completing your email and document verification to build trust within the community and unlock all features.

Ready to make a difference? Let's get started! 🚀

If you have any questions or need help getting started, don't hesitate to reach out to our support team.

© 2025 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

// Email verified notification template
const createEmailVerifiedTemplate = (userName) => {
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #3AD29F, #2D5A5A); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">✅ Email Verified!</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">Your profile is getting stronger</p>
      </div>

      <div style="padding: 30px 20px;">
        <h2 style="color: #2D5A5A;">Great news, ${userName || "there"}! 🎉</h2>

        <p style="color: #5A8A8A; line-height: 1.6;">
          Your email address has been successfully verified! This is an important step in building trust within the EvenOut community.
        </p>

        <div style="background: #F0F8F5; border-left: 4px solid #3AD29F; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
          <h3 style="color: #2D5A5A; margin: 0 0 10px 0;">🔒 Your Verification Badge Updated</h3>
          <p style="color: #5A8A8A; margin: 0;">
            Your profile now shows that your email is verified, making you more trustworthy to other community members.
          </p>
        </div>

        <div style="background: #FFF3E0; border-left: 4px solid #E6C068; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
          <h3 style="color: #B8860B; margin: 0 0 10px 0;">📄 Next Step: Document Verification</h3>
          <p style="color: #B8860B; margin: 0; font-size: 14px;">
            Complete your document verification to unlock all features and gain full community trust.
          </p>
        </div>

        <p style="color: #5A8A8A; line-height: 1.6;">
          You can now enjoy enhanced security and start building meaningful connections with your neighbors!
        </p>
      </div>

      <div style="text-align: center; padding: 20px; border-top: 1px solid #E0E0E0; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
✅ Email Verified!

Great news, ${userName || "there"}!

Your email address has been successfully verified! This is an important step in building trust within the EvenOut community.

🔒 Your Verification Badge Updated
Your profile now shows that your email is verified, making you more trustworthy to other community members.

📄 Next Step: Document Verification
Complete your document verification to unlock all features and gain full community trust.

You can now enjoy enhanced security and start building meaningful connections with your neighbors!

© 2024 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

// Document submitted notification template
const createDocumentSubmittedTemplate = (userName, documentType) => {
  const docType = documentType || "document";
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #3AD29F, #2D5A5A); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">📄 Document Submitted</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">We're reviewing your verification</p>
      </div>

      <div style="padding: 30px 20px;">
        <h2 style="color: #2D5A5A;">Thank you, ${userName || "there"}! 📋</h2>

        <p style="color: #5A8A8A; line-height: 1.6;">
          We've received your ${docType} for verification. Our team is now reviewing your submission to ensure everything meets our community standards.
        </p>

        <div style="background: #E3F2FD; border-left: 4px solid #2196F3; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
          <h3 style="color: #1976D2; margin: 0 0 10px 0;">⏱️ What Happens Next?</h3>
          <ul style="color: #1976D2; margin: 0; padding-left: 20px;">
            <li>Our verification team will review your document</li>
            <li>This process typically takes 1-3 business days</li>
            <li>You'll receive an email once verification is complete</li>
            <li>Your verification badge will be updated automatically</li>
          </ul>
        </div>

        <div style="background: #F0F8F5; border-left: 4px solid #3AD29F; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
          <h3 style="color: #2D5A5A; margin: 0 0 10px 0;">🚀 Meanwhile, Explore EvenOut!</h3>
          <p style="color: #5A8A8A; margin: 0;">
            While we review your documents, feel free to explore the app, browse items in your area, and start connecting with your community.
          </p>
        </div>

        <p style="color: #5A8A8A; line-height: 1.6;">
          If you have any questions about the verification process, don't hesitate to contact our support team.
        </p>
      </div>

      <div style="text-align: center; padding: 20px; border-top: 1px solid #E0E0E0; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
📄 Document Submitted

Thank you, ${userName || "there"}!

We've received your ${docType} for verification. Our team is now reviewing your submission to ensure everything meets our community standards.

⏱️ What Happens Next?
- Our verification team will review your document
- This process typically takes 1-3 business days
- You'll receive an email once verification is complete
- Your verification badge will be updated automatically

🚀 Meanwhile, Explore EvenOut!
While we review your documents, feel free to explore the app, browse items in your area, and start connecting with your community.

If you have any questions about the verification process, don't hesitate to contact our support team.

© 2025 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

// Document verified notification template
const createDocumentVerifiedTemplate = (userName, documentType) => {
  const docType = documentType || "documents";
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #3AD29F, #2D5A5A); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">🎉 Documents Verified!</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">You're now fully verified</p>
      </div>

      <div style="padding: 30px 20px;">
        <h2 style="color: #2D5A5A;">Congratulations, ${userName || "there"}! 🌟</h2>

        <p style="color: #5A8A8A; line-height: 1.6;">
          Your ${docType} have been successfully verified! You are now a fully verified member of the EvenOut community.
        </p>

        <div style="background: #F0F8F5; border-left: 4px solid #3AD29F; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
          <h3 style="color: #2D5A5A; margin: 0 0 10px 0;">🏆 Your Verification Badge Updated</h3>
          <p style="color: #5A8A8A; margin: 0;">
            Your profile now displays the full verification badge, showing other community members that you're a trusted user.
          </p>
        </div>

        <div style="background: #E8F5E8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
          <h3 style="color: #2E7D32; margin: 0 0 10px 0;">🔓 All Features Unlocked</h3>
          <ul style="color: #2E7D32; margin: 0; padding-left: 20px;">
            <li>List unlimited items for exchange</li>
            <li>Access premium community features</li>
            <li>Higher visibility in search results</li>
            <li>Priority customer support</li>
          </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <div style="background: #3AD29F; color: white; padding: 15px 30px; border-radius: 25px; display: inline-block; font-weight: bold;">
            Welcome to the verified community! 🎊
          </div>
        </div>

        <p style="color: #5A8A8A; line-height: 1.6;">
          Thank you for helping us build a trusted and secure community. Start exploring and making meaningful exchanges!
        </p>
      </div>

      <div style="text-align: center; padding: 20px; border-top: 1px solid #E0E0E0; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
🎉 Documents Verified!

Congratulations, ${userName || "there"}!

Your ${docType} have been successfully verified! You are now a fully verified member of the EvenOut community.

🏆 Your Verification Badge Updated
Your profile now displays the full verification badge, showing other community members that you're a trusted user.

🔓 All Features Unlocked
- List unlimited items for exchange
- Access premium community features
- Higher visibility in search results
- Priority customer support

Welcome to the verified community! 🎊

Thank you for helping us build a trusted and secure community. Start exploring and making meaningful exchanges!

© 2025 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

// Account deletion OTP template
const createAccountDeletionOTPTemplate = (otp, userName) => {
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #FF6B6B, #D32F2F); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">🔐 Account Deletion</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">Security Verification Required</p>
      </div>

      <div style="padding: 30px 20px;">
        <h2 style="color: #D32F2F;">Hello ${userName || "there"}, ⚠️</h2>

        <p style="color: #5A8A8A; line-height: 1.6;">
          We received a request to delete your EvenOut account. For your security, please verify this action with the code below.
        </p>

        <div style="background: #FFEBEE; border: 2px solid #FF6B6B; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0;">
          <div style="color: #D32F2F; font-size: 14px; margin-bottom: 10px;">ACCOUNT DELETION CODE</div>
          <div style="font-size: 32px; font-weight: bold; color: #D32F2F; letter-spacing: 4px; font-family: monospace;">${otp}</div>
          <div style="color: #D32F2F; font-size: 12px; margin-top: 10px;">Expires in 10 minutes</div>
        </div>

        <div style="background: #FFF3E0; border-left: 4px solid #FF9800; padding: 15px; margin: 20px 0;">
          <p style="color: #F57C00; margin: 0; font-size: 14px;">
            <strong>⚠️ Important:</strong> This action cannot be undone. All your data, listings, and connections will be permanently deleted.
          </p>
        </div>

        <div style="background: #FFEBEE; border-left: 4px solid #F44336; padding: 15px; margin: 20px 0;">
          <p style="color: #D32F2F; margin: 0; font-size: 14px;">
            <strong>🔒 Security Notice:</strong> If you didn't request this deletion, please ignore this email and contact support immediately.
          </p>
        </div>

        <p style="color: #5A8A8A; line-height: 1.6;">
          We're sad to see you go. If you're having issues with the app, our support team is here to help.
        </p>
      </div>

      <div style="text-align: center; padding: 20px; border-top: 1px solid #E0E0E0; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
🔐 Account Deletion - Security Verification Required

Hello ${userName || "there"},

We received a request to delete your EvenOut account. For your security, please verify this action with the code below.

ACCOUNT DELETION CODE: ${otp}
Expires in 10 minutes

⚠️ Important: This action cannot be undone. All your data, listings, and connections will be permanently deleted.

🔒 Security Notice: If you didn't request this deletion, please ignore this email and contact support immediately.

We're sad to see you go. If you're having issues with the app, our support team is here to help.

© 2025 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

// Swap request accepted email template for owner
const createSwapAcceptedOwnerTemplate = (ownerName, requesterName, itemName, requesterItemName) => {
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #3AD29F, #2D5A5A); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">🎉 Swap Request Accepted!</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">Your swap is confirmed</p>
      </div>

      <div style="padding: 30px; background: #F5F1E8; border-radius: 10px; margin-top: 20px;">
        <h2 style="color: #2D5A5A; margin-top: 0;">Hi ${ownerName || "there"},</h2>

        <p style="color: #2D5A5A; font-size: 16px; line-height: 1.6;">
          Great news! You've successfully accepted <strong>${requesterName}</strong>'s swap request for your <strong>${itemName}</strong>.
        </p>

        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3AD29F;">
          <h3 style="color: #2D5A5A; margin-top: 0;">📦 Swap Details:</h3>
          <p style="margin: 5px 0;"><strong>Your Item:</strong> ${itemName}</p>
          <p style="margin: 5px 0;"><strong>Their Item:</strong> ${requesterItemName || "Cash offer"}</p>
          <p style="margin: 5px 0;"><strong>Swap Partner:</strong> ${requesterName}</p>
        </div>

        <div style="background: #FFF3CD; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FFC107;">
          <h4 style="color: #856404; margin-top: 0;">⏰ Important Reminder:</h4>
          <p style="color: #856404; margin: 5px 0;">Your chat will close in 24 hours. Please exchange all important details (contact info, meeting location, etc.) before then.</p>
        </div>

        <h3 style="color: #2D5A5A;">🔄 Next Steps:</h3>
        <ul style="color: #2D5A5A; line-height: 1.8;">
          <li>Continue chatting to arrange pickup/delivery</li>
          <li>Share your contact details and preferred meeting location</li>
          <li>Confirm the condition and authenticity of items</li>
          <li>Complete the swap within the agreed timeframe</li>
        </ul>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://evenout.app/chat" style="background: #3AD29F; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">Continue Chat</a>
        </div>

        <p style="color: #5A8A8A; font-size: 14px; margin-top: 30px;">
          🌱 <strong>EvenOut Tip:</strong> Always meet in safe, public locations and verify items before completing the swap.
        </p>
      </div>

      <div style="text-align: center; padding: 20px; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
🎉 Swap Request Accepted!

Hi ${ownerName || "there"},

Great news! You've successfully accepted ${requesterName}'s swap request for your ${itemName}.

📦 Swap Details:
- Your Item: ${itemName}
- Their Item: ${requesterItemName || "Cash offer"}
- Swap Partner: ${requesterName}

⏰ Important Reminder:
Your chat will close in 24 hours. Please exchange all important details (contact info, meeting location, etc.) before then.

🔄 Next Steps:
- Continue chatting to arrange pickup/delivery
- Share your contact details and preferred meeting location
- Confirm the condition and authenticity of items
- Complete the swap within the agreed timeframe

🌱 EvenOut Tip: Always meet in safe, public locations and verify items before completing the swap.

© 2025 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

// Swap request accepted email template for requester
const createSwapAcceptedRequesterTemplate = (requesterName, ownerName, itemName, requesterItemName) => {
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #3AD29F, #2D5A5A); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">🎉 Your Swap Request Was Accepted!</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">Time to complete your swap</p>
      </div>

      <div style="padding: 30px; background: #F5F1E8; border-radius: 10px; margin-top: 20px;">
        <h2 style="color: #2D5A5A; margin-top: 0;">Hi ${requesterName || "there"},</h2>

        <p style="color: #2D5A5A; font-size: 16px; line-height: 1.6;">
          Fantastic! <strong>${ownerName}</strong> has accepted your swap request for their <strong>${itemName}</strong>. Your swap is now confirmed!
        </p>

        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3AD29F;">
          <h3 style="color: #2D5A5A; margin-top: 0;">📦 Swap Details:</h3>
          <p style="margin: 5px 0;"><strong>Item You're Getting:</strong> ${itemName}</p>
          <p style="margin: 5px 0;"><strong>Your Offer:</strong> ${requesterItemName || "Cash offer"}</p>
          <p style="margin: 5px 0;"><strong>Swap Partner:</strong> ${ownerName}</p>
        </div>

        <div style="background: #FFF3CD; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FFC107;">
          <h4 style="color: #856404; margin-top: 0;">⏰ Time Sensitive:</h4>
          <p style="color: #856404; margin: 5px 0;">Your chat will close in 24 hours. Please coordinate with ${ownerName} to arrange the swap details immediately.</p>
        </div>

        <h3 style="color: #2D5A5A;">🔄 What to Do Now:</h3>
        <ul style="color: #2D5A5A; line-height: 1.8;">
          <li>Message ${ownerName} to arrange pickup/delivery</li>
          <li>Share your contact details and availability</li>
          <li>Agree on a safe meeting location</li>
          <li>Prepare your item for the swap</li>
        </ul>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://evenout.app/chat" style="background: #3AD29F; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">Start Coordinating</a>
        </div>

        <p style="color: #5A8A8A; font-size: 14px; margin-top: 30px;">
          🌱 <strong>EvenOut Tip:</strong> Be responsive and flexible with timing to ensure a smooth swap experience.
        </p>
      </div>

      <div style="text-align: center; padding: 20px; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
🎉 Your Swap Request Was Accepted!

Hi ${requesterName || "there"},

Fantastic! ${ownerName} has accepted your swap request for their ${itemName}. Your swap is now confirmed!

📦 Swap Details:
- Item You're Getting: ${itemName}
- Your Offer: ${requesterItemName || "Cash offer"}
- Swap Partner: ${ownerName}

⏰ Time Sensitive:
Your chat will close in 24 hours. Please coordinate with ${ownerName} to arrange the swap details immediately.

🔄 What to Do Now:
- Message ${ownerName} to arrange pickup/delivery
- Share your contact details and availability
- Agree on a safe meeting location
- Prepare your item for the swap

🌱 EvenOut Tip: Be responsive and flexible with timing to ensure a smooth swap experience.

© 2025 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

// Swap request rejected email template for owner
const createSwapRejectedOwnerTemplate = (ownerName, requesterName, itemName) => {
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #FF6B6B, #D63384); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">📝 Swap Request Declined</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">Decision confirmed</p>
      </div>

      <div style="padding: 30px; background: #F5F1E8; border-radius: 10px; margin-top: 20px;">
        <h2 style="color: #2D5A5A; margin-top: 0;">Hi ${ownerName || "there"},</h2>

        <p style="color: #2D5A5A; font-size: 16px; line-height: 1.6;">
          You've declined <strong>${requesterName}</strong>'s swap request for your <strong>${itemName}</strong>. The chat has been closed and no further action is needed.
        </p>

        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF6B6B;">
          <h3 style="color: #2D5A5A; margin-top: 0;">📋 Summary:</h3>
          <p style="margin: 5px 0;"><strong>Your Item:</strong> ${itemName}</p>
          <p style="margin: 5px 0;"><strong>Requester:</strong> ${requesterName}</p>
          <p style="margin: 5px 0;"><strong>Status:</strong> Declined</p>
        </div>

        <h3 style="color: #2D5A5A;">✨ What's Next:</h3>
        <ul style="color: #2D5A5A; line-height: 1.8;">
          <li>Your item remains active and visible to other users</li>
          <li>You may receive new swap requests from other members</li>
          <li>The requester can make a new offer if they wish</li>
          <li>Continue browsing for items you'd like to swap</li>
        </ul>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://evenout.app/home" style="background: #3AD29F; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">Browse Items</a>
        </div>

        <p style="color: #5A8A8A; font-size: 14px; margin-top: 30px;">
          🌱 <strong>EvenOut Tip:</strong> Don't worry if this swap didn't work out - there are always more opportunities to find the perfect match!
        </p>
      </div>

      <div style="text-align: center; padding: 20px; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
📝 Swap Request Declined

Hi ${ownerName || "there"},

You've declined ${requesterName}'s swap request for your ${itemName}. The chat has been closed and no further action is needed.

📋 Summary:
- Your Item: ${itemName}
- Requester: ${requesterName}
- Status: Declined

✨ What's Next:
- Your item remains active and visible to other users
- You may receive new swap requests from other members
- The requester can make a new offer if they wish
- Continue browsing for items you'd like to swap

🌱 EvenOut Tip: Don't worry if this swap didn't work out - there are always more opportunities to find the perfect match!

© 2025 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

// Swap request rejected email template for requester
const createSwapRejectedRequesterTemplate = (requesterName, ownerName, itemName) => {
  const htmlTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #FF6B6B, #D63384); padding: 30px; text-align: center; border-radius: 10px;">
        <h1 style="color: white; margin: 0;">😔 Swap Request Not Accepted</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">But don't give up!</p>
      </div>

      <div style="padding: 30px; background: #F5F1E8; border-radius: 10px; margin-top: 20px;">
        <h2 style="color: #2D5A5A; margin-top: 0;">Hi ${requesterName || "there"},</h2>

        <p style="color: #2D5A5A; font-size: 16px; line-height: 1.6;">
          Unfortunately, <strong>${ownerName}</strong> has declined your swap request for their <strong>${itemName}</strong>. While this particular swap didn't work out, there are plenty of other opportunities waiting for you!
        </p>

        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF6B6B;">
          <h3 style="color: #2D5A5A; margin-top: 0;">📋 Request Details:</h3>
          <p style="margin: 5px 0;"><strong>Item:</strong> ${itemName}</p>
          <p style="margin: 5px 0;"><strong>Owner:</strong> ${ownerName}</p>
          <p style="margin: 5px 0;"><strong>Status:</strong> Not accepted</p>
        </div>

        <div style="background: #D1ECF1; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #17A2B8;">
          <h4 style="color: #0C5460; margin-top: 0;">💡 Good News:</h4>
          <p style="color: #0C5460; margin: 5px 0;">You can make a new swap request for this item anytime! The owner might reconsider with a different offer.</p>
        </div>

        <h3 style="color: #2D5A5A;">🚀 Keep Swapping:</h3>
        <ul style="color: #2D5A5A; line-height: 1.8;">
          <li>Browse thousands of other items in your area</li>
          <li>Try offering different items or cash</li>
          <li>Post more items to increase your swap options</li>
          <li>Connect with other community members</li>
        </ul>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://evenout.app/home" style="background: #3AD29F; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; margin-right: 10px;">Find New Items</a>
          <a href="https://evenout.app/list" style="background: #2D5A5A; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">List Your Items</a>
        </div>

        <p style="color: #5A8A8A; font-size: 14px; margin-top: 30px;">
          🌱 <strong>EvenOut Tip:</strong> Every "no" brings you closer to the perfect "yes"! Keep exploring and you'll find amazing swap opportunities.
        </p>
      </div>

      <div style="text-align: center; padding: 20px; color: #5A8A8A; font-size: 12px;">
        <p>© 2025 EvenOut - Building Sustainable Communities</p>
        <p>Contact <NAME_EMAIL></p>
      </div>
    </div>
  `;

  const textTemplate = `
😔 Swap Request Not Accepted

Hi ${requesterName || "there"},

Unfortunately, ${ownerName} has declined your swap request for their ${itemName}. While this particular swap didn't work out, there are plenty of other opportunities waiting for you!

📋 Request Details:
- Item: ${itemName}
- Owner: ${ownerName}
- Status: Not accepted

💡 Good News:
You can make a new swap request for this item anytime! The owner might reconsider with a different offer.

🚀 Keep Swapping:
- Browse thousands of other items in your area
- Try offering different items or cash
- Post more items to increase your swap options
- Connect with other community members

🌱 EvenOut Tip: Every "no" brings you closer to the perfect "yes"! Keep exploring and you'll find amazing swap opportunities.

© 2025 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
  `;

  return {
    html: htmlTemplate,
    text: textTemplate,
  };
};

/**
 * Send OTP Email Function
 * Generates and sends a 6-digit OTP to the user's email
 * Stores the OTP in Firestore with 5-minute expiration
 */
exports.sendOTPEmail = onCall(async (request) => {
  try {
    logger.info("sendOTPEmail function called", { data: request.data });

    // Validate input
    const {email, userName} = request.data;

    if (!email) {
      logger.error("Email is required");
      throw new Error("Email is required");
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      logger.error("Invalid email format", { email });
      throw new Error("Invalid email format");
    }

    logger.info("Generating OTP for email", { email, userName });

    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now

    logger.info("OTP generated", { otp, expiresAt });

    // Store OTP in Firestore
    const otpDoc = {
      email: email.toLowerCase(),
      otp: otp,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      expiresAt: expiresAt,
      verified: false,
      attempts: 0,
    };

    await admin.firestore()
        .collection("email_otps")
        .doc(email.toLowerCase())
        .set(otpDoc);

    // Create email template
    const emailTemplate = createOTPEmailTemplate(otp, userName);

    // Configure email options
    const mailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: email,
      subject: `${otp} is your EvenOut verification code`,
      html: emailTemplate.html,
      text: emailTemplate.text,
    };

    // Send email
    logger.info("Creating transporter and sending email");
    const transporter = createTransporter();
    logger.info("Transporter created, sending mail");

    const result = await transporter.sendMail(mailOptions);

    logger.info("OTP email sent successfully", {
      email: email,
      messageId: result.messageId,
      otp: otp, // Remove this in production for security
    });

    return {
      success: true,
      message: "OTP sent successfully",
      expiresAt: expiresAt.toISOString(),
    };
  } catch (error) {
    logger.error("Error sending OTP email", {
      error: error.message,
      stack: error.stack,
      errorType: error.constructor.name,
    });

    throw new Error(`Failed to send OTP: ${error.message}`);
  }
});

/**
 * Send Welcome Email Function
 * Sends a welcome email to new users after successful signup
 */
exports.sendWelcomeEmail = onCall(async (request) => {
  try {
    logger.info("sendWelcomeEmail function called", { data: request.data });

    const { email, userName } = request.data;

    if (!email) {
      logger.error("Email is required for welcome email");
      throw new Error("Email is required");
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      logger.error("Invalid email format", { email });
      throw new Error("Invalid email format");
    }

    logger.info("Sending welcome email", { email, userName });

    // Create welcome email template
    const welcomeEmailTemplate = createWelcomeEmailTemplate(userName);

    // Configure email options
    const mailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: email,
      subject: "Welcome to EvenOut - Let's Build Sustainable Communities Together! 🌱",
      html: welcomeEmailTemplate.html,
      text: welcomeEmailTemplate.text,
    };

    // Send email
    logger.info("Sending welcome email");
    const transporter = createTransporter();
    const result = await transporter.sendMail(mailOptions);

    logger.info("Welcome email sent successfully", {
      email: email,
      messageId: result.messageId,
    });

    return {
      success: true,
      message: "Welcome email sent successfully",
    };
  } catch (error) {
    logger.error("Error sending welcome email", {
      error: error.message,
      stack: error.stack,
      errorType: error.constructor.name,
    });

    throw new Error(`Failed to send welcome email: ${error.message}`);
  }
});

/**
 * Verify OTP Function
 * Verifies the OTP provided by the user
 */
exports.verifyOTP = onCall(async (request) => {
  try {
    const {email, otp} = request.data;

    if (!email || !otp) {
      throw new Error("Email and OTP are required");
    }

    // Get OTP document from Firestore
    const otpDocRef = admin.firestore()
        .collection("email_otps")
        .doc(email.toLowerCase());

    const otpDoc = await otpDocRef.get();

    if (!otpDoc.exists) {
      throw new Error("No OTP found for this email");
    }

    const otpData = otpDoc.data();

    // Check if OTP is already verified
    if (otpData.verified) {
      throw new Error("OTP has already been used");
    }

    // Check if OTP is expired
    if (new Date() > otpData.expiresAt.toDate()) {
      throw new Error("OTP has expired");
    }

    // Check attempt limit (max 3 attempts)
    if (otpData.attempts >= 3) {
      throw new Error("Too many failed attempts. Please request a new OTP");
    }

    // Verify OTP
    if (otpData.otp !== otp.toString()) {
      // Increment attempt counter
      await otpDocRef.update({
        attempts: admin.firestore.FieldValue.increment(1),
      });

      throw new Error("Invalid OTP");
    }

    // Mark OTP as verified
    await otpDocRef.update({
      verified: true,
      verifiedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    logger.info("OTP verified successfully", {
      email: email,
    });

    return {
      success: true,
      message: "OTP verified successfully",
    };
  } catch (error) {
    logger.error("Error verifying OTP", {
      error: error.message,
      email: request.data && request.data.email,
    });

    throw new Error(`OTP verification failed: ${error.message}`);
  }
});

/**
 * Send Email Verified Notification
 * Sends confirmation email when user's email is verified
 */
exports.sendEmailVerifiedNotification = onCall(async (request) => {
  try {
    logger.info("sendEmailVerifiedNotification function called", { data: request.data });

    const { email, userName } = request.data;

    if (!email) {
      throw new Error("Email is required");
    }

    const emailTemplate = createEmailVerifiedTemplate(userName);

    const mailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: email,
      subject: "✅ Email Verified - Your EvenOut Profile is Getting Stronger!",
      html: emailTemplate.html,
      text: emailTemplate.text,
    };

    const transporter = createTransporter();
    const result = await transporter.sendMail(mailOptions);

    logger.info("Email verified notification sent successfully", {
      email: email,
      messageId: result.messageId,
    });

    return {
      success: true,
      message: "Email verified notification sent successfully",
    };
  } catch (error) {
    logger.error("Error sending email verified notification", {
      error: error.message,
      stack: error.stack,
    });

    throw new Error(`Failed to send email verified notification: ${error.message}`);
  }
});

/**
 * Send Document Submitted Notification
 * Sends confirmation email when user submits documents for verification
 */
exports.sendDocumentSubmittedNotification = onCall(async (request) => {
  try {
    logger.info("sendDocumentSubmittedNotification function called", { data: request.data });

    const { email, userName, documentType } = request.data;

    if (!email) {
      throw new Error("Email is required");
    }

    const emailTemplate = createDocumentSubmittedTemplate(userName, documentType);

    const mailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: email,
      subject: "📄 Document Submitted - We're Reviewing Your Verification",
      html: emailTemplate.html,
      text: emailTemplate.text,
    };

    const transporter = createTransporter();
    const result = await transporter.sendMail(mailOptions);

    logger.info("Document submitted notification sent successfully", {
      email: email,
      messageId: result.messageId,
    });

    return {
      success: true,
      message: "Document submitted notification sent successfully",
    };
  } catch (error) {
    logger.error("Error sending document submitted notification", {
      error: error.message,
      stack: error.stack,
    });

    throw new Error(`Failed to send document submitted notification: ${error.message}`);
  }
});

/**
 * Send Document Verified Notification
 * Sends confirmation email when user's documents are verified
 */
exports.sendDocumentVerifiedNotification = onCall(async (request) => {
  try {
    logger.info("sendDocumentVerifiedNotification function called", { data: request.data });

    const { email, userName, documentType } = request.data;

    if (!email) {
      throw new Error("Email is required");
    }

    const emailTemplate = createDocumentVerifiedTemplate(userName, documentType);

    const mailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: email,
      subject: "🎉 Documents Verified - You're Fully Verified on EvenOut!",
      html: emailTemplate.html,
      text: emailTemplate.text,
    };

    const transporter = createTransporter();
    const result = await transporter.sendMail(mailOptions);

    logger.info("Document verified notification sent successfully", {
      email: email,
      messageId: result.messageId,
    });

    return {
      success: true,
      message: "Document verified notification sent successfully",
    };
  } catch (error) {
    logger.error("Error sending document verified notification", {
      error: error.message,
      stack: error.stack,
    });

    throw new Error(`Failed to send document verified notification: ${error.message}`);
  }
});

/**
 * Send Account Deletion OTP
 * Sends OTP for account deletion verification (double security)
 */
exports.sendAccountDeletionOTP = onCall(async (request) => {
  try {
    logger.info("sendAccountDeletionOTP function called", { data: request.data });

    const { email, userName } = request.data;

    if (!email) {
      throw new Error("Email is required");
    }

    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes for account deletion

    // Store OTP in Firestore with different collection for account deletion
    const otpDoc = {
      email: email.toLowerCase(),
      otp: otp,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      expiresAt: expiresAt,
      verified: false,
      attempts: 0,
      purpose: "account_deletion",
    };

    await admin.firestore()
        .collection("account_deletion_otps")
        .doc(email.toLowerCase())
        .set(otpDoc);

    const emailTemplate = createAccountDeletionOTPTemplate(otp, userName);

    const mailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: email,
      subject: `🔐 ${otp} - Account Deletion Verification Code`,
      html: emailTemplate.html,
      text: emailTemplate.text,
    };

    const transporter = createTransporter();
    const result = await transporter.sendMail(mailOptions);

    logger.info("Account deletion OTP sent successfully", {
      email: email,
      messageId: result.messageId,
    });

    return {
      success: true,
      message: "Account deletion OTP sent successfully",
      expiresAt: expiresAt.toISOString(),
    };
  } catch (error) {
    logger.error("Error sending account deletion OTP", {
      error: error.message,
      stack: error.stack,
    });

    throw new Error(`Failed to send account deletion OTP: ${error.message}`);
  }
});

/**
 * Verify Account Deletion OTP
 * Verifies OTP for account deletion
 */
exports.verifyAccountDeletionOTP = onCall(async (request) => {
  try {
    const { email, otp } = request.data;

    if (!email || !otp) {
      throw new Error("Email and OTP are required");
    }

    const otpDocRef = admin.firestore()
        .collection("account_deletion_otps")
        .doc(email.toLowerCase());

    const otpDoc = await otpDocRef.get();

    if (!otpDoc.exists) {
      throw new Error("No OTP found for this email");
    }

    const otpData = otpDoc.data();

    if (otpData.verified) {
      throw new Error("OTP has already been used");
    }

    if (new Date() > otpData.expiresAt.toDate()) {
      throw new Error("OTP has expired");
    }

    if (otpData.attempts >= 3) {
      throw new Error("Too many failed attempts. Please request a new OTP");
    }

    if (otpData.otp !== otp.toString()) {
      await otpDocRef.update({
        attempts: admin.firestore.FieldValue.increment(1),
      });
      throw new Error("Invalid OTP");
    }

    await otpDocRef.update({
      verified: true,
      verifiedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    logger.info("Account deletion OTP verified successfully", {
      email: email,
    });

    return {
      success: true,
      message: "Account deletion OTP verified successfully",
    };
  } catch (error) {
    logger.error("Error verifying account deletion OTP", {
      error: error.message,
      email: request.data && request.data.email,
    });

    throw new Error(`Account deletion OTP verification failed: ${error.message}`);
  }
});

/**
 * Send Swap Request Accepted Notification Emails
 * Sends different emails to owner and requester when swap is accepted
 */
exports.sendSwapAcceptedEmails = onCall(async (request) => {
  try {
    logger.info("sendSwapAcceptedEmails function called", { data: request.data });

    const {
      ownerEmail,
      ownerName,
      requesterEmail,
      requesterName,
      itemName,
      requesterItemName
    } = request.data;

    if (!ownerEmail || !requesterEmail || !itemName) {
      throw new Error("Required fields missing: ownerEmail, requesterEmail, itemName");
    }

    const transporter = createTransporter();

    // Send email to owner
    const ownerEmailTemplate = createSwapAcceptedOwnerTemplate(ownerName, requesterName, itemName, requesterItemName);
    const ownerMailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: ownerEmail,
      subject: "🎉 Swap Request Accepted - Time to Coordinate!",
      html: ownerEmailTemplate.html,
      text: ownerEmailTemplate.text,
    };

    // Send email to requester
    const requesterEmailTemplate = createSwapAcceptedRequesterTemplate(requesterName, ownerName, itemName, requesterItemName);
    const requesterMailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: requesterEmail,
      subject: "🎉 Your Swap Request Was Accepted!",
      html: requesterEmailTemplate.html,
      text: requesterEmailTemplate.text,
    };

    // Send both emails
    const [ownerResult, requesterResult] = await Promise.all([
      transporter.sendMail(ownerMailOptions),
      transporter.sendMail(requesterMailOptions)
    ]);

    logger.info("Swap accepted emails sent successfully", {
      ownerEmail: ownerEmail,
      requesterEmail: requesterEmail,
      ownerMessageId: ownerResult.messageId,
      requesterMessageId: requesterResult.messageId,
    });

    return {
      success: true,
      message: "Swap accepted emails sent successfully",
    };
  } catch (error) {
    logger.error("Error sending swap accepted emails", { error: error.message });
    throw new Error(`Failed to send swap accepted emails: ${error.message}`);
  }
});

/**
 * Send Swap Request Rejected Notification Emails
 * Sends different emails to owner and requester when swap is rejected
 */
exports.sendSwapRejectedEmails = onCall(async (request) => {
  try {
    logger.info("sendSwapRejectedEmails function called", { data: request.data });

    const {
      ownerEmail,
      ownerName,
      requesterEmail,
      requesterName,
      itemName
    } = request.data;

    if (!ownerEmail || !requesterEmail || !itemName) {
      throw new Error("Required fields missing: ownerEmail, requesterEmail, itemName");
    }

    const transporter = createTransporter();

    // Send email to owner
    const ownerEmailTemplate = createSwapRejectedOwnerTemplate(ownerName, requesterName, itemName);
    const ownerMailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: ownerEmail,
      subject: "📝 Swap Request Declined - Confirmed",
      html: ownerEmailTemplate.html,
      text: ownerEmailTemplate.text,
    };

    // Send email to requester
    const requesterEmailTemplate = createSwapRejectedRequesterTemplate(requesterName, ownerName, itemName);
    const requesterMailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: requesterEmail,
      subject: "💔 Swap Request Update - Keep Exploring!",
      html: requesterEmailTemplate.html,
      text: requesterEmailTemplate.text,
    };

    // Send both emails
    const [ownerResult, requesterResult] = await Promise.all([
      transporter.sendMail(ownerMailOptions),
      transporter.sendMail(requesterMailOptions)
    ]);

    logger.info("Swap rejected emails sent successfully", {
      ownerEmail: ownerEmail,
      requesterEmail: requesterEmail,
      ownerMessageId: ownerResult.messageId,
      requesterMessageId: requesterResult.messageId,
    });

    return {
      success: true,
      message: "Swap rejected emails sent successfully",
    };
  } catch (error) {
    logger.error("Error sending swap rejected emails", { error: error.message });
    throw new Error(`Failed to send swap rejected emails: ${error.message}`);
  }
});

/**
 * Cleanup expired OTPs (runs daily)
 * This function can be triggered by a scheduled job
 */
exports.cleanupExpiredOTPs = onRequest(async (req, res) => {
  try {
    const now = new Date();
    const expiredOTPs = await admin.firestore()
        .collection("email_otps")
        .where("expiresAt", "<", now)
        .get();

    const batch = admin.firestore().batch();
    expiredOTPs.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();

    logger.info(`Cleaned up ${expiredOTPs.size} expired OTPs`);

    res.status(200).json({
      success: true,
      message: `Cleaned up ${expiredOTPs.size} expired OTPs`,
    });
  } catch (error) {
    logger.error("Error cleaning up expired OTPs", {
      error: error.message,
    });

    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
