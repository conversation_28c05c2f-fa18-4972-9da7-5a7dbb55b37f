{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "echo '<PERSON><PERSON> skipped for deployment'", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"crypto": "^1.0.1", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "nodemailer": "^7.0.5"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}