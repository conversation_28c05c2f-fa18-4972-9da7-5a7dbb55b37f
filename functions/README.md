# EvenOut Firebase Functions

This directory contains Firebase Cloud Functions for the EvenOut app, including OTP email functionality.

## Setup Instructions

### 1. Gmail App Password Setup

To send emails through Gmail SMTP, you need to create an App Password:

1. **Enable 2-Factor Authentication** on your Gmail account (<EMAIL>)
2. Go to **Google Account Settings** → **Security**
3. Under **2-Step Verification**, click **App passwords**
4. Select **Mail** as the app and **Other** as the device
5. Enter "EvenOut Firebase Functions" as the device name
6. Copy the generated 16-character password

### 2. Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your Gmail App Password:
   ```
   GMAIL_USER=<EMAIL>
   GMAIL_APP_PASSWORD=your_16_character_app_password
   ```

### 3. Deploy Functions

Deploy the functions to Firebase:

```bash
# From the functions directory
npm run deploy

# Or from the project root
firebase deploy --only functions
```

## Available Functions

### 1. `sendOTPEmail`
- **Type**: Callable Function
- **Purpose**: Sends a 6-digit OTP to user's email
- **Parameters**:
  - `email` (string, required): User's email address
  - `userName` (string, optional): User's display name
- **Returns**: Success status and expiration time

### 2. `verifyOTP`
- **Type**: Callable Function  
- **Purpose**: Verifies the OTP provided by user
- **Parameters**:
  - `email` (string, required): User's email address
  - `otp` (string, required): 6-digit OTP code
- **Returns**: Success status

### 3. `cleanupExpiredOTPs`
- **Type**: HTTP Function
- **Purpose**: Removes expired OTP records from Firestore
- **Usage**: Can be called manually or set up as a scheduled job

## Security Features

- **5-minute expiration**: OTPs expire after 5 minutes
- **Attempt limiting**: Maximum 3 verification attempts per OTP
- **One-time use**: OTPs cannot be reused after verification
- **Email validation**: Validates email format before sending
- **Secure storage**: OTPs are stored securely in Firestore

## Email Template

The OTP emails include:
- Professional EvenOut branding
- Clear OTP display with large, readable font
- Security warnings and instructions
- Mobile-responsive design
- Both HTML and plain text versions

## Testing

You can test the functions locally using the Firebase emulator:

```bash
npm run serve
```

## Firestore Collections

### `email_otps`
Stores OTP verification data:
- `email`: User's email (document ID)
- `otp`: 6-digit verification code
- `createdAt`: Timestamp when OTP was created
- `expiresAt`: Timestamp when OTP expires
- `verified`: Boolean indicating if OTP was used
- `attempts`: Number of verification attempts
- `verifiedAt`: Timestamp when OTP was verified (if applicable)

## Error Handling

The functions include comprehensive error handling for:
- Invalid email formats
- Expired OTPs
- Too many attempts
- Network failures
- Gmail SMTP errors

## Monitoring

All functions log important events and errors to Firebase Functions logs for monitoring and debugging.
