{"project_info": {"project_number": "1037152015645", "firebase_url": "https://evenout-b1432-default-rtdb.firebaseio.com", "project_id": "evenout-b1432", "storage_bucket": "evenout-b1432.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:1037152015645:android:84d70136a13295892cbdf4", "android_client_info": {"package_name": "com.evenout.evenout"}}, "oauth_client": [{"client_id": "1037152015645-l3i895345k463ukj8ao375vsvh1rs3a3.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.evenout.evenout", "certificate_hash": "62798758b6e4abc7093095c424327ef64d9e7bde"}}, {"client_id": "1037152015645-b0dhfgb2i7act8r1le0sqhvvu40klg2j.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAiLzibmDLzoqyA9s5RCm9v9F_9JacGCCk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1037152015645-b0dhfgb2i7act8r1le0sqhvvu40klg2j.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1037152015645-mgjmrpbjtki79b0296dq5radmt7nu14l.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.evenout.evenout"}}]}}}], "configuration_version": "1"}