{"flutter": {"platforms": {"android": {"default": {"projectId": "evenout-b1432", "appId": "1:1037152015645:android:84d70136a13295892cbdf4", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "evenout-b1432", "appId": "1:1037152015645:ios:bd5f531e9a9d21222cbdf4", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "evenout-b1432", "configurations": {"android": "1:1037152015645:android:84d70136a13295892cbdf4", "ios": "1:1037152015645:ios:bd5f531e9a9d21222cbdf4", "web": "1:1037152015645:web:8ec1110d88f040152cbdf4"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}]}