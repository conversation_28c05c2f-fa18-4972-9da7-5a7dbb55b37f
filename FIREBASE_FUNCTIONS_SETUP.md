# Firebase Functions OTP Email Setup Guide

## 🚀 Complete Setup Instructions

### Step 1: Gmail App Password Setup

1. **Go to your Gmail account** (<EMAIL>)
2. **Enable 2-Factor Authentication** if not already enabled
3. **Go to Google Account Settings** → **Security** → **2-Step Verification**
4. **Click "App passwords"**
5. **Select "Mail"** and **"Other (Custom name)"**
6. **Enter "EvenOut Firebase Functions"** as the name
7. **Copy the 16-character password** (it will look like: `abcd efgh ijkl mnop`)

### Step 2: Set Firebase Configuration

Run this command to set the Gmail password in Firebase:

```bash
firebase functions:config:set gmail.password="your_16_character_app_password_here"
```

**Replace `your_16_character_app_password_here` with the actual app password from Step 1**

### Step 3: Deploy Functions

Deploy the functions to Firebase:

```bash
firebase deploy --only functions
```

### Step 4: Test the Functions

After deployment, you can test the functions:

#### Test OTP Email Sending:
```javascript
// In your Flutter app or Firebase console
const functions = firebase.functions();
const sendOTP = functions.httpsCallable('sendOTPEmail');

sendOTP({
  email: '<EMAIL>',
  userName: 'Test User'
}).then((result) => {
  console.log('OTP sent:', result.data);
}).catch((error) => {
  console.error('Error:', error);
});
```

#### Test OTP Verification:
```javascript
const verifyOTP = functions.httpsCallable('verifyOTP');

verifyOTP({
  email: '<EMAIL>',
  otp: '123456'
}).then((result) => {
  console.log('OTP verified:', result.data);
}).catch((error) => {
  console.error('Error:', error);
});
```

## 📧 Function Details

### `sendOTPEmail`
- **Purpose**: Sends a 6-digit OTP to user's email
- **Input**: `{ email: string, userName?: string }`
- **Output**: `{ success: boolean, message: string, expiresAt: string }`

### `verifyOTP`
- **Purpose**: Verifies the OTP code
- **Input**: `{ email: string, otp: string }`
- **Output**: `{ success: boolean, message: string }`

### `cleanupExpiredOTPs`
- **Purpose**: Removes expired OTP records
- **Type**: HTTP endpoint
- **URL**: `https://us-central1-evenout-b1432.cloudfunctions.net/cleanupExpiredOTPs`

## 🔒 Security Features

- ✅ **5-minute expiration** - OTPs expire automatically
- ✅ **3 attempt limit** - Prevents brute force attacks
- ✅ **One-time use** - OTPs cannot be reused
- ✅ **Email validation** - Validates email format
- ✅ **Secure storage** - OTPs stored in Firestore with encryption

## 📱 Integration with Flutter App

Add this to your Flutter app's `pubspec.yaml`:

```yaml
dependencies:
  cloud_functions: ^5.1.3
```

Then use in your Dart code:

```dart
import 'package:cloud_functions/cloud_functions.dart';

class OTPService {
  static final FirebaseFunctions _functions = FirebaseFunctions.instance;

  static Future<Map<String, dynamic>> sendOTP({
    required String email,
    String? userName,
  }) async {
    try {
      final callable = _functions.httpsCallable('sendOTPEmail');
      final result = await callable.call({
        'email': email,
        'userName': userName,
      });
      return result.data;
    } catch (e) {
      throw Exception('Failed to send OTP: $e');
    }
  }

  static Future<Map<String, dynamic>> verifyOTP({
    required String email,
    required String otp,
  }) async {
    try {
      final callable = _functions.httpsCallable('verifyOTP');
      final result = await callable.call({
        'email': email,
        'otp': otp,
      });
      return result.data;
    } catch (e) {
      throw Exception('Failed to verify OTP: $e');
    }
  }
}
```

## 🐛 Troubleshooting

### Common Issues:

1. **"Authentication failed"**
   - Check if the Gmail app password is correct
   - Ensure 2FA is enabled on the Gmail account

2. **"Function not found"**
   - Make sure functions are deployed: `firebase deploy --only functions`
   - Check Firebase console for deployment status

3. **"Permission denied"**
   - Ensure Firebase project is properly configured
   - Check IAM permissions in Google Cloud Console

4. **"OTP not received"**
   - Check spam/junk folder
   - Verify email address is correct
   - Check Firebase Functions logs for errors

### View Logs:
```bash
firebase functions:log
```

## 📊 Monitoring

Monitor your functions in:
- **Firebase Console** → **Functions** → **Logs**
- **Google Cloud Console** → **Cloud Functions**

## 🔄 Updates

To update the functions:
1. Modify the code in `functions/index.js`
2. Run `firebase deploy --only functions`
3. Test the updated functionality

---

**Need Help?** Check the Firebase Functions documentation or contact support.
