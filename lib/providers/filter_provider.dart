import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Filter types available for items
enum FilterType {
  nearMe('Near Me', Icons.location_on),
  recentlyAdded('Recently Added', Icons.schedule),
  freeToSwap('Free to Swap', Icons.money_off),
  permanent('Permanent', Icons.all_inclusive),
  temporary('Temporary', Icons.schedule_outlined),
  electronics('Electronics', Icons.devices),
  clothing('Clothing', Icons.checkroom),
  books('Books', Icons.book),
  sports('Sports', Icons.sports),
  furniture('Furniture', Icons.chair);

  const FilterType(this.label, this.icon);
  final String label;
  final IconData icon;
}

/// Filter state model
class FilterState {
  final FilterType? selectedFilter;
  final List<FilterType> filterOrder;
  final bool isAnimating;

  const FilterState({
    this.selectedFilter,
    this.filterOrder = const [
      FilterType.nearMe,
      FilterType.recentlyAdded,
      FilterType.freeToSwap,
      FilterType.permanent,
      FilterType.temporary,
      FilterType.electronics,
      FilterType.clothing,
      FilterType.books,
      FilterType.sports,
      FilterType.furniture,
    ],
    this.isAnimating = false,
  });

  FilterState copyWith({
    FilterType? selectedFilter,
    List<FilterType>? filterOrder,
    bool? isAnimating,
  }) {
    return FilterState(
      selectedFilter: selectedFilter,
      filterOrder: filterOrder ?? this.filterOrder,
      isAnimating: isAnimating ?? this.isAnimating,
    );
  }
}

/// Filter provider notifier
class FilterNotifier extends StateNotifier<FilterState> {
  FilterNotifier() : super(const FilterState());

  /// Select a filter and move it to first position with animation
  Future<void> selectFilter(FilterType filter) async {
    if (state.selectedFilter == filter) {
      // Unselect if already selected
      state = state.copyWith(selectedFilter: null);
      return;
    }

    // Start animation
    state = state.copyWith(isAnimating: true);

    // Create new order with selected filter first
    final newOrder = List<FilterType>.from(state.filterOrder);
    newOrder.remove(filter);
    newOrder.insert(0, filter);

    // Update state with new selection and order
    state = state.copyWith(
      selectedFilter: filter,
      filterOrder: newOrder,
    );

    // End animation after a delay
    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      state = state.copyWith(isAnimating: false);
    }
  }

  /// Clear selected filter
  void clearFilter() {
    state = state.copyWith(selectedFilter: null);
  }

  /// Filter items based on selected filter
  List<Map<String, dynamic>> filterItems(
    List<Map<String, dynamic>> items,
    FilterType? filter,
  ) {
    if (filter == null) return items;

    switch (filter) {
      case FilterType.nearMe:
        // Sort by distance (already sorted in provider, but ensure closest first)
        final sortedItems = List<Map<String, dynamic>>.from(items);
        sortedItems.sort((a, b) {
          final distanceA = a['distance'] as double? ?? double.infinity;
          final distanceB = b['distance'] as double? ?? double.infinity;
          return distanceA.compareTo(distanceB);
        });
        return sortedItems.take(20).toList(); // Show only closest 20 items

      case FilterType.recentlyAdded:
        // Sort by creation date (most recent first)
        final sortedItems = List<Map<String, dynamic>>.from(items);
        sortedItems.sort((a, b) {
          final timestampA = a['createdAt'] as dynamic;
          final timestampB = b['createdAt'] as dynamic;
          
          DateTime? dateA;
          DateTime? dateB;
          
          if (timestampA != null) {
            dateA = timestampA is DateTime ? timestampA : timestampA.toDate();
          }
          if (timestampB != null) {
            dateB = timestampB is DateTime ? timestampB : timestampB.toDate();
          }
          
          if (dateA == null && dateB == null) return 0;
          if (dateA == null) return 1;
          if (dateB == null) return -1;
          
          return dateB.compareTo(dateA);
        });
        return sortedItems;

      case FilterType.freeToSwap:
        // Filter items with price 0 or null
        return items.where((item) {
          final price = item['price'] as double?;
          return price == null || price == 0.0;
        }).toList();

      case FilterType.permanent:
        // Filter permanent swap items
        return items.where((item) {
          final swapOption = item['swapOption'] as String?;
          return swapOption?.toLowerCase() == 'permanent';
        }).toList();

      case FilterType.temporary:
        // Filter temporary swap items
        return items.where((item) {
          final swapOption = item['swapOption'] as String?;
          return swapOption?.toLowerCase() == 'temporary';
        }).toList();

      case FilterType.electronics:
      case FilterType.clothing:
      case FilterType.books:
      case FilterType.sports:
      case FilterType.furniture:
        // Filter by category
        final categoryName = filter.label.toLowerCase();
        return items.where((item) {
          final category = item['category'] as String?;
          return category?.toLowerCase() == categoryName;
        }).toList();
    }
  }
}

/// Filter provider instance
final filterProvider = StateNotifierProvider<FilterNotifier, FilterState>((ref) {
  return FilterNotifier();
});
