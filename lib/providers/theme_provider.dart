import 'package:flutter/material.dart';
import '../services/theme_service.dart';

class ThemeProvider extends ChangeNotifier {
  bool _isDarkTheme = false;

  bool get isDarkTheme => _isDarkTheme;

  ThemeProvider() {
    _loadTheme();
  }

  /// Loads the saved theme preference
  Future<void> _loadTheme() async {
    _isDarkTheme = await ThemeService.isDarkTheme();
    notifyListeners();
  }

  /// Toggles between light and dark theme
  Future<void> toggleTheme() async {
    _isDarkTheme = await ThemeService.toggleTheme();
    notifyListeners();
  }

  /// Sets the theme explicitly
  Future<void> setTheme(bool isDark) async {
    if (_isDarkTheme != isDark) {
      _isDarkTheme = isDark;
      await ThemeService.setTheme(isDark);
      notifyListeners();
    }
  }
}
