import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';

/// Liked items state model
class LikedItemsState {
  final int count;
  final bool isLoading;
  final Set<String> likedItemIds;

  const LikedItemsState({
    this.count = 0,
    this.isLoading = false,
    this.likedItemIds = const {},
  });

  LikedItemsState copyWith({
    int? count,
    bool? isLoading,
    Set<String>? likedItemIds,
  }) {
    return LikedItemsState(
      count: count ?? this.count,
      isLoading: isLoading ?? this.isLoading,
      likedItemIds: likedItemIds ?? this.likedItemIds,
    );
  }
}

/// Liked items provider notifier
class LikedItemsNotifier extends StateNotifier<LikedItemsState> {
  LikedItemsNotifier() : super(const LikedItemsState()) {
    _loadLikedItems();
  }

  /// Load liked items count and IDs
  Future<void> _loadLikedItems() async {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return;

    state = state.copyWith(isLoading: true);

    try {
      final querySnapshot = await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .collection('likedItems')
          .get();

      final likedIds = querySnapshot.docs.map((doc) => doc.id).toSet();

      if (mounted) {
        state = state.copyWith(
          count: querySnapshot.docs.length,
          likedItemIds: likedIds,
          isLoading: false,
        );
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(isLoading: false);
      }
    }
  }

  /// Add item to liked items
  Future<void> likeItem(String itemId, Map<String, dynamic> itemData) async {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return;

    try {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .collection('likedItems')
          .doc(itemId)
          .set({
        'itemId': itemId,
        'likedAt': FieldValue.serverTimestamp(),
        'itemData': itemData,
      });

      // Update state immediately
      final newLikedIds = Set<String>.from(state.likedItemIds);
      newLikedIds.add(itemId);

      if (mounted) {
        state = state.copyWith(
          count: state.count + 1,
          likedItemIds: newLikedIds,
        );
      }
    } catch (e) {
      // Handle error silently or show error message
      rethrow;
    }
  }

  /// Remove item from liked items
  Future<void> unlikeItem(String itemId) async {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return;

    try {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .collection('likedItems')
          .doc(itemId)
          .delete();

      // Update state immediately
      final newLikedIds = Set<String>.from(state.likedItemIds);
      newLikedIds.remove(itemId);

      if (mounted) {
        state = state.copyWith(
          count: state.count - 1,
          likedItemIds: newLikedIds,
        );
      }
    } catch (e) {
      // Handle error silently or show error message
      rethrow;
    }
  }

  /// Check if item is liked
  bool isItemLiked(String itemId) {
    return state.likedItemIds.contains(itemId);
  }

  /// Refresh liked items
  Future<void> refresh() async {
    await _loadLikedItems();
  }
}

/// Liked items provider instance
final likedItemsProvider = StateNotifierProvider<LikedItemsNotifier, LikedItemsState>((ref) {
  return LikedItemsNotifier();
});
