import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../themes/app_theme.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: AppTheme.getBackgroundColor(context),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back,
            color: AppTheme.getTextColor(context),
          ),
        ),
        title: Text(
          'Settings',
          style: TextStyle(
            color: AppTheme.getTextColor(context),
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'App Settings',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Customize your app experience',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.getSecondaryTextColor(context),
              ),
            ),
            const SizedBox(height: 24),

            // Settings Container
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppTheme.getSurfaceColor(context),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Dark Theme Toggle
                    _buildSettingsOption(
                      context: context,
                      icon: Icons.dark_mode_outlined,
                      title: 'Dark Theme',
                      subtitle: 'Switch between light and dark theme',
                      trailing: Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return Switch(
                            value: themeProvider.isDarkTheme,
                            onChanged: (value) {
                              themeProvider.toggleTheme();
                            },
                            activeColor: AppTheme.getPrimaryColor(context),
                            activeTrackColor: AppTheme.getPrimaryColor(context).withValues(alpha: 0.3),
                            inactiveThumbColor: AppTheme.getSecondaryTextColor(context),
                            inactiveTrackColor: AppTheme.getBorderColor(context),
                          );
                        },
                      ),
                    ),
                    _buildDivider(context),

                    // Notifications
                    _buildSettingsOption(
                      context: context,
                      icon: Icons.notifications_outlined,
                      title: 'Notifications',
                      subtitle: 'Manage your notification preferences',
                      onTap: () => _showComingSoon(context),
                    ),
                    _buildDivider(context),

                    // Privacy
                    _buildSettingsOption(
                      context: context,
                      icon: Icons.privacy_tip_outlined,
                      title: 'Privacy Policy',
                      subtitle: 'View our privacy policy',
                      onTap: () => _showComingSoon(context),
                    ),
                    _buildDivider(context),

                    // Location
                    _buildSettingsOption(
                      context: context,
                      icon: Icons.location_on_outlined,
                      title: 'Location',
                      subtitle: 'Manage location permissions',
                      onTap: () => _showComingSoon(context),
                    ),
                    _buildDivider(context),

                    // App Version
                    _buildSettingsOption(
                      context: context,
                      icon: Icons.info_outline,
                      title: 'App Version',
                      subtitle: '1.0.0',
                      onTap: () => _showAppInfoDialog(context),
                    ),
                    _buildDivider(context),

                    // Terms of Service
                    _buildSettingsOption(
                      context: context,
                      icon: Icons.description_outlined,
                      title: 'Terms of Service',
                      subtitle: 'View our terms of service',
                      onTap: () => _showComingSoon(context),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.getPrimaryColor(context),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.getTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              trailing ?? (onTap != null
                ? Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppTheme.getSecondaryTextColor(context),
                  )
                : const SizedBox.shrink()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      height: 1,
      thickness: 1,
      color: AppTheme.getBorderColor(context),
      indent: 16,
      endIndent: 16,
    );
  }

  void _showComingSoon(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Feature coming soon!'),
        backgroundColor: AppTheme.getPrimaryColor(context),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showAppInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppTheme.getSurfaceColor(context),
          title: Text(
            'About EvenOut',
            style: TextStyle(
              color: AppTheme.getTextColor(context),
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'EvenOut is a platform for swapping items with others in your community. '
            'Share what you have and find what you need!\n\n'
            'Version: 1.0.0\n'
            'Built with Flutter',
            style: TextStyle(
              color: AppTheme.getTextColor(context),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'OK',
                style: TextStyle(
                  color: AppTheme.getPrimaryColor(context),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
