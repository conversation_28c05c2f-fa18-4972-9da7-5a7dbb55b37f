import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../themes/app_theme.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: AppTheme.getBackgroundColor(context),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back,
            color: AppTheme.getTextColor(context),
          ),
        ),
        title: Text(
          'Settings',
          style: TextStyle(
            color: AppTheme.getTextColor(context),
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Appearance Section
            Text(
              'Appearance',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 16),

            // Dark Theme Toggle
            _buildSettingsOption(
              context: context,
              icon: Icons.dark_mode_outlined,
              title: 'Dark Theme',
              subtitle: 'Switch between light and dark theme',
              trailing: Consumer<ThemeProvider>(
                builder: (context, themeProvider, child) {
                  return Switch(
                    value: themeProvider.isDarkTheme,
                    onChanged: (value) {
                      themeProvider.toggleTheme();
                    },
                    activeColor: AppTheme.getPrimaryColor(context),
                    activeTrackColor: AppTheme.getPrimaryColor(context).withValues(alpha: 0.3),
                    inactiveThumbColor: AppTheme.getSecondaryTextColor(context),
                    inactiveTrackColor: AppTheme.getBorderColor(context),
                  );
                },
              ),
            ),

            const SizedBox(height: 32),

            // About Section
            Text(
              'About',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 16),

            // App Version
            _buildSettingsOption(
              context: context,
              icon: Icons.info_outline,
              title: 'App Version',
              subtitle: '1.0.0',
              onTap: () {
                // Show app info dialog
                _showAppInfoDialog(context);
              },
            ),

            // Privacy Policy
            _buildSettingsOption(
              context: context,
              icon: Icons.privacy_tip_outlined,
              title: 'Privacy Policy',
              subtitle: 'View our privacy policy',
              onTap: () {
                // TODO: Navigate to privacy policy
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('Privacy Policy - Coming Soon'),
                    backgroundColor: AppTheme.getPrimaryColor(context),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),

            // Terms of Service
            _buildSettingsOption(
              context: context,
              icon: Icons.description_outlined,
              title: 'Terms of Service',
              subtitle: 'View our terms of service',
              onTap: () {
                // TODO: Navigate to terms of service
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('Terms of Service - Coming Soon'),
                    backgroundColor: AppTheme.getPrimaryColor(context),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.getBorderColor(context),
          width: 1,
        ),
      ),
      child: ListTile(
        onTap: onTap,
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: AppTheme.getPrimaryColor(context),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: AppTheme.getTextColor(context),
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.getSecondaryTextColor(context),
          ),
        ),
        trailing: trailing ?? (onTap != null
          ? Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppTheme.getSecondaryTextColor(context),
            )
          : null),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  void _showAppInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppTheme.getSurfaceColor(context),
          title: Text(
            'About EvenOut',
            style: TextStyle(
              color: AppTheme.getTextColor(context),
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'EvenOut is a platform for swapping items with others in your community. '
            'Share what you have and find what you need!\n\n'
            'Version: 1.0.0\n'
            'Built with Flutter',
            style: TextStyle(
              color: AppTheme.getTextColor(context),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'OK',
                style: TextStyle(
                  color: AppTheme.getPrimaryColor(context),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
