import 'package:flutter/material.dart';
import '../themes/app_theme.dart';
import '../widgets/app_divider.dart';
import '../services/support_service.dart';
import '../services/gemini_chat_service.dart';
import '../widgets/bug_report_modal.dart';
import '../widgets/feedback_modal.dart';
import 'support_chat_screen.dart';

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: AppTheme.getBackgroundColor(context),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back,
            color: AppTheme.getTextColor(context),
          ),
        ),
        title: Text(
          'Help & Support',
          style: TextStyle(
            color: AppTheme.getTextColor(context),
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(18, 10, 18, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'How can we help you?',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Find answers to common questions or get in touch with our support team.',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.getSecondaryTextColor(context),
              ),
            ),
            const SizedBox(height: 32),

            // Help Options Container
            Container(
              decoration: BoxDecoration(
                color: AppTheme.getSurfaceColor(context),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  _buildHelpOption(
                    context: context,
                    icon: Icons.question_answer_outlined,
                    title: 'Frequently Asked Questions',
                    subtitle: 'Find answers to common questions',
                    onTap: () => _showFAQModal(context),
                  ),
                  const AppDivider(),
                  _buildHelpOption(
                    context: context,
                    icon: Icons.email_outlined,
                    title: 'Contact Support',
                    subtitle: 'Send us an email for assistance',
                    onTap: () => _contactSupport(),
                  ),
                  const AppDivider(),
                  _buildHelpOption(
                    context: context,
                    icon: Icons.phone_outlined,
                    title: 'Call Support',
                    subtitle: 'Speak directly with our team',
                    onTap: () => _callSupport(),
                  ),
                  const AppDivider(),
                  _buildHelpOption(
                    context: context,
                    icon: Icons.chat_outlined,
                    title: 'Live Chat',
                    subtitle: 'Chat with our support team',
                    onTap: () => _startLiveChat(context),
                  ),
                  const AppDivider(),
                  _buildHelpOption(
                    context: context,
                    icon: Icons.bug_report_outlined,
                    title: 'Report a Bug',
                    subtitle: 'Help us improve the app',
                    onTap: () => _reportBug(context),
                  ),
                  const AppDivider(),
                  _buildHelpOption(
                    context: context,
                    icon: Icons.feedback_outlined,
                    title: 'Send Feedback',
                    subtitle: 'Share your thoughts and suggestions',
                    onTap: () => _sendFeedback(context),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.getPrimaryColor(context),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.getTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppTheme.getSecondaryTextColor(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Action methods
  void _showFAQModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _FAQModal(),
    );
  }

  Future<void> _contactSupport() async {
    try {
      await SupportService.contactSupport();
    } catch (e) {
      // Handle error - show fallback contact info
      _showContactInfo();
    }
  }

  Future<void> _callSupport() async {
    try {
      await SupportService.callSupport();
    } catch (e) {
      // Handle error - show fallback phone info
      _showPhoneInfo();
    }
  }

  Future<void> _startLiveChat(BuildContext context) async {
    try {
      // Initialize Gemini AI if not already done
      await GeminiChatService.initialize();

      // Create new support chat session
      final chatId = await GeminiChatService.createSupportChatSession();

      // Navigate to chat screen
      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => SupportChatScreen(chatId: chatId),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting live chat: $e'),
            backgroundColor: AppTheme.getErrorColor(context),
          ),
        );
      }
    }
  }

  void _reportBug(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const BugReportModal(),
    );
  }

  void _sendFeedback(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const FeedbackModal(),
    );
  }

  void _showContactInfo() {
    // Fallback method to show contact information
    // This will be implemented if URL launcher fails
  }

  void _showPhoneInfo() {
    // Fallback method to show phone information
    // This will be implemented if URL launcher fails
  }


}

// FAQ Modal
class _FAQModal extends StatelessWidget {
  final List<Map<String, String>> faqs = [
    {
      'question': 'How do I create a swap request?',
      'answer': 'Go to any item detail page and tap the "Offer to Swap" button. You can then select items from your collection to offer in exchange.',
    },
    {
      'question': 'How do I verify my profile?',
      'answer': 'Go to Profile > Profile Verification and complete the phone, email, and document verification steps.',
    },
    {
      'question': 'What items can I list for swapping?',
      'answer': 'You can list any items you own including electronics, clothing, books, sports equipment, and furniture.',
    },
    {
      'question': 'How do I contact other users?',
      'answer': 'Once you send a swap request, a chat will be automatically created where you can communicate with the other user.',
    },
    {
      'question': 'Is my personal information safe?',
      'answer': 'Yes, we take privacy seriously. Your data is encrypted and we never share your personal information with third parties.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: AppTheme.getBackgroundColor(context),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.getBorderColor(context),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  Icons.question_answer,
                  color: AppTheme.getPrimaryColor(context),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Frequently Asked Questions',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.getTextColor(context),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: AppTheme.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),

          // FAQ List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: faqs.length,
              itemBuilder: (context, index) {
                final faq = faqs[index];
                return _FAQItem(
                  question: faq['question']!,
                  answer: faq['answer']!,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _FAQItem extends StatefulWidget {
  final String question;
  final String answer;

  const _FAQItem({
    required this.question,
    required this.answer,
  });

  @override
  State<_FAQItem> createState() => _FAQItemState();
}

class _FAQItemState extends State<_FAQItem> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.getBorderColor(context),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.question,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.getTextColor(context),
                        ),
                      ),
                    ),
                    AnimatedRotation(
                      turns: _isExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 200),
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        color: AppTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (_isExpanded)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Text(
                widget.answer,
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.getSecondaryTextColor(context),
                  height: 1.5,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
