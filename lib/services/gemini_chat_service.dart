import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_ai/firebase_ai.dart';

class GeminiChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseDatabase _realtimeDb = FirebaseDatabase.instance;
  static GenerativeModel? _model;
  static bool _isInitialized = false;

  // Initialize Firebase AI with proper Gemini integration
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Firebase AI with Google AI backend (Gemini Developer API)
      final ai = FirebaseAI.googleAI();

      // Create a generative model with system instructions for EvenOut support
      _model = ai.generativeModel(
        model: 'gemini-2.5-flash',
        systemInstruction: Content.text('''
You are EvenOut Support Assistant, a helpful AI assistant for the EvenOut app - a platform for swapping and trading items.

IMPORTANT: You have full access to the user's data in the EvenOut system. When users ask about their personal information, account status, items, messages, or any app-related data, you can fetch and provide specific details from their account.

Your role:
- Help users with app-related questions and provide step-by-step guidance
- Assist with item listing, swapping, and trading processes
- Help with profile verification and account issues
- Provide guidance on location-based item discovery
- Assist with chat and messaging features
- Address safety and security concerns
- Troubleshoot technical issues
- Access and provide user's personal data when requested

Key EvenOut features to help with:
- Item listing and management (posting, editing, deleting items)
- Swapping and trading items (how to make offers, accept/reject swaps)
- Profile verification (phone, email, document verification)
- Location-based item discovery (finding nearby items)
- Chat and messaging (communicating with other users)
- Safety and security (reporting issues, staying safe)

User Data Access:
- Profile information (name, email, phone, verification status)
- Posted items (active, inactive, sold items)
- Swap requests (sent, received, pending, completed)
- Messages and chat history
- Location and preferences
- Account settings and activity
- Verification documents and status

When users ask about their data:
- Fetch real information from their account
- Provide specific details (item names, dates, statuses)
- Reference actual data points (verification status, item counts, etc.)
- Use their actual name and information in responses
- Be specific about dates, numbers, and statuses

Guidelines:
- Be friendly, helpful, and professional
- Provide clear, step-by-step instructions when possible
- Keep responses concise but informative (aim for 2-3 sentences)
- Use real user data when available to personalize responses
- If you can't help with something specific, suggest contacting human support
- Always prioritize user safety and security
- Encourage users to follow app guidelines and best practices
- When a user's problem seems resolved, ask if they need anything else or if the chat can be closed

Chat closure guidelines:
- If the user says "thank you", "thanks", "that's all", "no more questions", or similar, ask: "Is there anything else I can help you with, or would you like me to close this chat?"
- If the user confirms they're done or says "close chat", "end chat", "that's all", respond with a closing message
- If the user seems satisfied with the solution and hasn't asked follow-up questions, proactively ask if they need anything else

Remember: You're representing EvenOut, so maintain a positive and supportive tone while being helpful and efficient. Use the user's actual data to provide personalized, accurate assistance.
        '''),
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 500,
        ),
      );

      _isInitialized = true;
      print('Firebase AI (Gemini) service initialized successfully');
    } catch (e) {
      print('Error initializing Firebase AI: $e');
      rethrow;
    }
  }

  // Create a new support chat session using Realtime Database with Firestore sync
  static Future<String> createSupportChatSession() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw 'User not authenticated';

    try {
      // Create chat session in Firestore for basic info storage
      final chatDoc = await _firestore.collection('support_chats').add({
        'userId': user.uid,
        'userName': user.displayName ?? 'User',
        'userEmail': user.email,
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'active',
        'lastMessageAt': FieldValue.serverTimestamp(),
        'isAIChat': true,
        'messageCount': 0,
      });

      final chatId = chatDoc.id;

      // Create chat session in Realtime Database for real-time messaging
      await _realtimeDb.ref('support_chats/$chatId').set({
        'userId': user.uid,
        'userName': user.displayName ?? 'User',
        'userEmail': user.email,
        'createdAt': ServerValue.timestamp,
        'status': 'active',
        'lastMessageAt': ServerValue.timestamp,
        'isAIChat': true,
      });

      // Send welcome message
      await _sendAIMessage(
        chatId,
        "Hello! I'm EvenOut Support Assistant. I'm here to help you with any questions about the EvenOut app. How can I assist you today?",
      );

      return chatId;
    } catch (e) {
      print('Error creating support chat: $e');
      rethrow;
    }
  }

  // Send user message and get AI response
  static Future<void> sendMessage(String chatId, String message) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw 'User not authenticated';

    try {
      // Save user message to Realtime Database
      final messageRef = _realtimeDb.ref('support_chats/$chatId/messages').push();
      await messageRef.set({
        'senderId': user.uid,
        'senderName': user.displayName ?? 'User',
        'message': message,
        'timestamp': ServerValue.timestamp,
        'type': 'user',
      });

      // Update chat last message time in both databases
      await _realtimeDb.ref('support_chats/$chatId').update({
        'lastMessageAt': ServerValue.timestamp,
      });

      await _firestore.collection('support_chats').doc(chatId).update({
        'lastMessageAt': FieldValue.serverTimestamp(),
        'messageCount': FieldValue.increment(1),
      });

      // Get AI response
      await _getAIResponse(chatId, message);
    } catch (e) {
      print('Error sending message: $e');
      rethrow;
    }
  }

  // Get AI response using Firebase AI (Gemini) with user data access
  static Future<void> _getAIResponse(String chatId, String userMessage) async {
    if (!_isInitialized || _model == null) {
      await initialize();
    }

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw 'User not authenticated';

      // Get recent chat history for context (last 10 messages)
      final messagesSnapshot = await _realtimeDb
          .ref('support_chats/$chatId/messages')
          .orderByChild('timestamp')
          .limitToLast(10)
          .once();

      // Fetch comprehensive user data
      final userData = await _fetchUserData(user.uid);

      // Check if user is asking for specific data and fetch additional details
      final specificData = await _fetchSpecificUserData(user.uid, userMessage);

      // Build conversation context with user data
      List<Content> conversationHistory = [];

      // Add user data context for AI
      conversationHistory.add(Content.text('''
CURRENT USER DATA:
${userData}

${specificData.isNotEmpty ? 'ADDITIONAL SPECIFIC DATA:\n$specificData' : ''}

Use this data to provide personalized responses when the user asks about their account, items, verification status, or any personal information. Always reference specific details from their actual data when answering questions about their account.
'''));

      if (messagesSnapshot.snapshot.value != null) {
        final messagesMap = messagesSnapshot.snapshot.value as Map<dynamic, dynamic>;
        final sortedMessages = messagesMap.entries.toList()
          ..sort((a, b) => (a.value['timestamp'] as int).compareTo(b.value['timestamp'] as int));

        for (var entry in sortedMessages) {
          final messageData = entry.value as Map<dynamic, dynamic>;
          final isUser = messageData['type'] == 'user';
          final messageText = messageData['message'] as String;

          if (isUser) {
            conversationHistory.add(Content.text('User: $messageText'));
          } else {
            conversationHistory.add(Content.text('Assistant: $messageText'));
          }
        }
      }

      // Add current user message
      conversationHistory.add(Content.text('User: $userMessage'));

      // Generate AI response using Firebase AI
      final response = await _model!.generateContent(conversationHistory);
      final aiMessage = response.text ?? 'I apologize, but I\'m having trouble responding right now. Please try again or contact our human support team.';

      // Check if AI suggests closing the chat
      final shouldOfferClosure = _shouldOfferChatClosure(userMessage, aiMessage);
      final finalMessage = shouldOfferClosure
          ? '$aiMessage\n\nIs there anything else I can help you with, or would you like me to close this chat?'
          : aiMessage;

      // Save AI response
      await _sendAIMessage(chatId, finalMessage);
    } catch (e) {
      print('Error getting AI response: $e');
      // Send fallback message
      await _sendAIMessage(
        chatId,
        'I apologize, but I\'m experiencing technical difficulties. Please try again in a moment or contact our human support team for immediate assistance.',
      );
    }
  }

  // Fetch comprehensive user data from Firestore
  static Future<String> _fetchUserData(String userId) async {
    try {
      final userData = StringBuffer();

      // Get user profile
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final user = userDoc.data()!;
        userData.writeln('USER PROFILE:');
        userData.writeln('- Name: ${user['name'] ?? 'Not set'}');
        userData.writeln('- Email: ${user['email'] ?? 'Not set'}');
        userData.writeln('- Phone: ${user['phone'] ?? 'Not set'}');
        userData.writeln('- City: ${user['city'] ?? 'Not set'}');
        userData.writeln('- Date of Birth: ${user['dateOfBirth'] ?? 'Not set'}');
        userData.writeln('- Account Created: ${user['createdAt']?.toDate() ?? 'Unknown'}');
        userData.writeln('- Profile Picture: ${user['profilePicture'] != null ? 'Set' : 'Not set'}');
        userData.writeln('');

        // Verification status
        userData.writeln('VERIFICATION STATUS:');
        userData.writeln('- Phone Verified: ${user['phoneVerified'] ?? false}');
        userData.writeln('- Email Verified: ${user['emailVerified'] ?? false}');
        userData.writeln('- Document Verified: ${user['documentVerified'] ?? false}');
        userData.writeln('- Verification Level: ${user['verificationLevel'] ?? 'Basic'}');
        userData.writeln('');
      }

      // Get user's posted items
      final itemsQuery = await _firestore
          .collection('items')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(20)
          .get();

      userData.writeln('POSTED ITEMS (${itemsQuery.docs.length} total):');
      for (var doc in itemsQuery.docs) {
        final item = doc.data();
        userData.writeln('- ${item['name']} (${item['category']}) - Status: ${item['status'] ?? 'active'} - Posted: ${item['createdAt']?.toDate()}');
      }
      userData.writeln('');

      // Get swap requests sent by user
      final sentSwapsQuery = await _firestore
          .collection('swap_requests')
          .where('requesterId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      userData.writeln('SWAP REQUESTS SENT (${sentSwapsQuery.docs.length} recent):');
      for (var doc in sentSwapsQuery.docs) {
        final swap = doc.data();
        userData.writeln('- Item: ${swap['itemName']} - Status: ${swap['status']} - Date: ${swap['createdAt']?.toDate()}');
      }
      userData.writeln('');

      // Get swap requests received by user
      final receivedSwapsQuery = await _firestore
          .collection('swap_requests')
          .where('ownerId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      userData.writeln('SWAP REQUESTS RECEIVED (${receivedSwapsQuery.docs.length} recent):');
      for (var doc in receivedSwapsQuery.docs) {
        final swap = doc.data();
        userData.writeln('- Item: ${swap['itemName']} - From: ${swap['requesterName']} - Status: ${swap['status']} - Date: ${swap['createdAt']?.toDate()}');
      }
      userData.writeln('');

      // Get user's chat sessions
      final chatsQuery = await _firestore
          .collection('chats')
          .where('participants', arrayContains: userId)
          .orderBy('lastMessageAt', descending: true)
          .limit(10)
          .get();

      userData.writeln('RECENT CHATS (${chatsQuery.docs.length} active):');
      for (var doc in chatsQuery.docs) {
        final chat = doc.data();
        userData.writeln('- Chat ID: ${doc.id} - Last Message: ${chat['lastMessageAt']?.toDate()} - Type: ${chat['type'] ?? 'swap'}');
      }
      userData.writeln('');

      // Get user's search history
      final searchQuery = await _firestore
          .collection('search_history')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(5)
          .get();

      if (searchQuery.docs.isNotEmpty) {
        userData.writeln('RECENT SEARCHES:');
        for (var doc in searchQuery.docs) {
          final search = doc.data();
          userData.writeln('- "${search['query']}" - ${search['timestamp']?.toDate()}');
        }
        userData.writeln('');
      }

      // Get user's location preferences
      final preferencesDoc = await _firestore.collection('user_preferences').doc(userId).get();
      if (preferencesDoc.exists) {
        final prefs = preferencesDoc.data()!;
        userData.writeln('USER PREFERENCES:');
        userData.writeln('- Search Radius: ${prefs['searchRadius'] ?? '20'} km');
        userData.writeln('- Preferred Categories: ${prefs['preferredCategories'] ?? 'All'}');
        userData.writeln('- Notifications Enabled: ${prefs['notificationsEnabled'] ?? true}');
        userData.writeln('- Location Sharing: ${prefs['locationSharing'] ?? true}');
        userData.writeln('');
      }

      return userData.toString();
    } catch (e) {
      print('Error fetching user data: $e');
      return 'USER DATA: Unable to fetch user data at this time.';
    }
  }

  // Fetch additional user data for specific queries
  static Future<String> _fetchSpecificUserData(String userId, String query) async {
    try {
      final queryLower = query.toLowerCase();
      final userData = StringBuffer();

      // Check what specific data the user is asking about
      if (queryLower.contains('verification') || queryLower.contains('verify')) {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        if (userDoc.exists) {
          final user = userDoc.data()!;
          userData.writeln('DETAILED VERIFICATION STATUS:');
          userData.writeln('- Phone Number: ${user['phone'] ?? 'Not provided'}');
          userData.writeln('- Phone Verified: ${user['phoneVerified'] ?? false}');
          userData.writeln('- Email: ${user['email'] ?? 'Not provided'}');
          userData.writeln('- Email Verified: ${user['emailVerified'] ?? false}');
          userData.writeln('- Document Type: ${user['documentType'] ?? 'Not submitted'}');
          userData.writeln('- Document Status: ${user['documentVerified'] ? 'Verified' : 'Pending/Not submitted'}');
          userData.writeln('- Verification Level: ${user['verificationLevel'] ?? 'Basic'}');
          userData.writeln('- Document Submitted Date: ${user['documentSubmittedAt']?.toDate() ?? 'Not submitted'}');
          userData.writeln('- Document Verified Date: ${user['documentVerifiedAt']?.toDate() ?? 'Not verified'}');
        }
      }

      if (queryLower.contains('item') || queryLower.contains('post')) {
        final itemsQuery = await _firestore
            .collection('items')
            .where('userId', isEqualTo: userId)
            .orderBy('createdAt', descending: true)
            .get();

        final activeItems = itemsQuery.docs.where((doc) => doc.data()['status'] == 'active').length;
        final inactiveItems = itemsQuery.docs.where((doc) => doc.data()['status'] == 'inactive').length;
        final soldItems = itemsQuery.docs.where((doc) => doc.data()['status'] == 'sold').length;

        userData.writeln('DETAILED ITEMS INFORMATION:');
        userData.writeln('- Total Items Posted: ${itemsQuery.docs.length}');
        userData.writeln('- Active Items: $activeItems');
        userData.writeln('- Inactive Items: $inactiveItems');
        userData.writeln('- Sold Items: $soldItems');
        userData.writeln('');

        userData.writeln('RECENT ITEMS:');
        for (var doc in itemsQuery.docs.take(10)) {
          final item = doc.data();
          userData.writeln('- ${item['name']} (${item['category']})');
          userData.writeln('  Status: ${item['status'] ?? 'active'}');
          userData.writeln('  Posted: ${item['createdAt']?.toDate()}');
          userData.writeln('  Views: ${item['views'] ?? 0}');
          userData.writeln('  Likes: ${item['likes'] ?? 0}');
          userData.writeln('');
        }
      }

      if (queryLower.contains('swap') || queryLower.contains('offer') || queryLower.contains('trade')) {
        // Get detailed swap information
        final sentSwapsQuery = await _firestore
            .collection('swap_requests')
            .where('requesterId', isEqualTo: userId)
            .orderBy('createdAt', descending: true)
            .get();

        final receivedSwapsQuery = await _firestore
            .collection('swap_requests')
            .where('ownerId', isEqualTo: userId)
            .orderBy('createdAt', descending: true)
            .get();

        final pendingSent = sentSwapsQuery.docs.where((doc) => doc.data()['status'] == 'pending').length;
        final acceptedSent = sentSwapsQuery.docs.where((doc) => doc.data()['status'] == 'accepted').length;
        final rejectedSent = sentSwapsQuery.docs.where((doc) => doc.data()['status'] == 'rejected').length;

        final pendingReceived = receivedSwapsQuery.docs.where((doc) => doc.data()['status'] == 'pending').length;
        final acceptedReceived = receivedSwapsQuery.docs.where((doc) => doc.data()['status'] == 'accepted').length;
        final rejectedReceived = receivedSwapsQuery.docs.where((doc) => doc.data()['status'] == 'rejected').length;

        userData.writeln('DETAILED SWAP INFORMATION:');
        userData.writeln('REQUESTS SENT:');
        userData.writeln('- Total Sent: ${sentSwapsQuery.docs.length}');
        userData.writeln('- Pending: $pendingSent');
        userData.writeln('- Accepted: $acceptedSent');
        userData.writeln('- Rejected: $rejectedSent');
        userData.writeln('');

        userData.writeln('REQUESTS RECEIVED:');
        userData.writeln('- Total Received: ${receivedSwapsQuery.docs.length}');
        userData.writeln('- Pending: $pendingReceived');
        userData.writeln('- Accepted: $acceptedReceived');
        userData.writeln('- Rejected: $rejectedReceived');
        userData.writeln('');

        userData.writeln('RECENT SWAP ACTIVITY:');
        final allSwaps = [...sentSwapsQuery.docs, ...receivedSwapsQuery.docs];
        allSwaps.sort((a, b) => (b.data()['createdAt'] as Timestamp).compareTo(a.data()['createdAt'] as Timestamp));

        for (var doc in allSwaps.take(10)) {
          final swap = doc.data();
          final isSent = swap['requesterId'] == userId;
          userData.writeln('- ${isSent ? 'SENT' : 'RECEIVED'}: ${swap['itemName']}');
          userData.writeln('  ${isSent ? 'To' : 'From'}: ${isSent ? swap['ownerName'] : swap['requesterName']}');
          userData.writeln('  Status: ${swap['status']}');
          userData.writeln('  Date: ${swap['createdAt']?.toDate()}');
          userData.writeln('');
        }
      }

      if (queryLower.contains('message') || queryLower.contains('chat')) {
        final chatsQuery = await _firestore
            .collection('chats')
            .where('participants', arrayContains: userId)
            .orderBy('lastMessageAt', descending: true)
            .get();

        userData.writeln('DETAILED CHAT INFORMATION:');
        userData.writeln('- Total Active Chats: ${chatsQuery.docs.length}');
        userData.writeln('');

        userData.writeln('RECENT CHAT ACTIVITY:');
        for (var doc in chatsQuery.docs.take(10)) {
          final chat = doc.data();
          final participants = List<String>.from(chat['participants'] ?? []);
          final otherParticipant = participants.firstWhere((p) => p != userId, orElse: () => 'Unknown');

          userData.writeln('- Chat with: ${chat['otherUserName'] ?? otherParticipant}');
          userData.writeln('  Last Message: ${chat['lastMessageAt']?.toDate()}');
          userData.writeln('  Type: ${chat['type'] ?? 'swap'}');
          userData.writeln('  Messages: ${chat['messageCount'] ?? 0}');
          userData.writeln('');
        }
      }

      if (queryLower.contains('location') || queryLower.contains('address')) {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        if (userDoc.exists) {
          final user = userDoc.data()!;
          userData.writeln('LOCATION INFORMATION:');
          userData.writeln('- City: ${user['city'] ?? 'Not set'}');
          userData.writeln('- Current Location: ${user['currentLocation'] != null ? 'Set' : 'Not available'}');
          userData.writeln('- Location Sharing: ${user['locationSharing'] ?? 'Enabled'}');

          if (user['currentLocation'] != null) {
            final location = user['currentLocation'];
            userData.writeln('- Coordinates: ${location['latitude']}, ${location['longitude']}');
            userData.writeln('- Last Updated: ${user['locationUpdatedAt']?.toDate() ?? 'Unknown'}');
          }
        }
      }

      return userData.toString();
    } catch (e) {
      print('Error fetching specific user data: $e');
      return 'Unable to fetch specific user data at this time.';
    }
  }

  // Check if chat closure should be offered based on user message and AI response
  static bool _shouldOfferChatClosure(String userMessage, String aiResponse) {
    final message = userMessage.toLowerCase();
    final response = aiResponse.toLowerCase();

    // Offer closure if user expresses satisfaction or completion
    if (message.contains('thank') || message.contains('thanks') ||
        message.contains('that\'s all') || message.contains('no more') ||
        message.contains('perfect') || message.contains('great') ||
        message.contains('solved') || message.contains('fixed')) {
      return true;
    }

    // Offer closure if AI response seems to complete a solution
    if (response.contains('you should now be able') ||
        response.contains('that should resolve') ||
        response.contains('this should fix') ||
        response.contains('you\'re all set')) {
      return true;
    }

    return false;
  }

  // Send AI message to chat using Realtime Database
  static Future<void> _sendAIMessage(String chatId, String message) async {
    try {
      // Save AI message to Realtime Database
      final messageRef = _realtimeDb.ref('support_chats/$chatId/messages').push();
      await messageRef.set({
        'senderId': 'ai_assistant',
        'senderName': 'EvenOut Support',
        'message': message,
        'timestamp': ServerValue.timestamp,
        'type': 'ai',
      });

      // Update chat last message time in both databases
      await _realtimeDb.ref('support_chats/$chatId').update({
        'lastMessageAt': ServerValue.timestamp,
      });

      await _firestore.collection('support_chats').doc(chatId).update({
        'lastMessageAt': FieldValue.serverTimestamp(),
        'messageCount': FieldValue.increment(1),
      });
    } catch (e) {
      print('Error sending AI message: $e');
      rethrow;
    }
  }

  // Get chat messages stream from Realtime Database
  static Stream<DatabaseEvent> getChatMessages(String chatId) {
    return _realtimeDb
        .ref('support_chats/$chatId/messages')
        .orderByChild('timestamp')
        .onValue;
  }

  // Close support chat in both databases
  static Future<void> closeSupportChat(String chatId) async {
    try {
      // Update status in both databases
      await _realtimeDb.ref('support_chats/$chatId').update({
        'status': 'closed',
        'closedAt': ServerValue.timestamp,
      });

      await _firestore.collection('support_chats').doc(chatId).update({
        'status': 'closed',
        'closedAt': FieldValue.serverTimestamp(),
      });

      // Send closing message
      await _sendAIMessage(
        chatId,
        'Thank you for using EvenOut Support! This chat session has been closed. If you need further assistance, feel free to start a new chat anytime.',
      );
    } catch (e) {
      print('Error closing support chat: $e');
      rethrow;
    }
  }
}
