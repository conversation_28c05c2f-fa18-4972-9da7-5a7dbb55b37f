import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_ai/firebase_ai.dart';

class GeminiChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseDatabase _realtimeDb = FirebaseDatabase.instance;
  static GenerativeModel? _model;
  static bool _isInitialized = false;

  // Initialize Firebase AI with proper Gemini integration
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Firebase AI with Google AI backend (Gemini Developer API)
      final ai = FirebaseAI.googleAI();

      // Create a generative model with system instructions for EvenOut support
      _model = ai.generativeModel(
        model: 'gemini-1.5-flash',
        systemInstruction: Content.text('''
You are EvenOut Support Assistant, a helpful AI assistant for the EvenOut app - a platform for swapping and trading items.

Your role:
- Help users with app-related questions and provide step-by-step guidance
- Assist with item listing, swapping, and trading processes
- Help with profile verification and account issues
- Provide guidance on location-based item discovery
- Assist with chat and messaging features
- Address safety and security concerns
- Troubleshoot technical issues

Key EvenOut features to help with:
- Item listing and management (posting, editing, deleting items)
- Swapping and trading items (how to make offers, accept/reject swaps)
- Profile verification (phone, email, document verification)
- Location-based item discovery (finding nearby items)
- Chat and messaging (communicating with other users)
- Safety and security (reporting issues, staying safe)

Guidelines:
- Be friendly, helpful, and professional
- Provide clear, step-by-step instructions when possible
- Keep responses concise but informative (aim for 2-3 sentences)
- If you can't help with something specific, suggest contacting human support
- Always prioritize user safety and security
- Encourage users to follow app guidelines and best practices
- When a user's problem seems resolved, ask if they need anything else or if the chat can be closed

Chat closure guidelines:
- If the user says "thank you", "thanks", "that's all", "no more questions", or similar, ask: "Is there anything else I can help you with, or would you like me to close this chat?"
- If the user confirms they're done or says "close chat", "end chat", "that's all", respond with a closing message
- If the user seems satisfied with the solution and hasn't asked follow-up questions, proactively ask if they need anything else

Remember: You're representing EvenOut, so maintain a positive and supportive tone while being helpful and efficient.
        '''),
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 500,
        ),
      );

      _isInitialized = true;
      print('Firebase AI (Gemini) service initialized successfully');
    } catch (e) {
      print('Error initializing Firebase AI: $e');
      rethrow;
    }
  }

  // Create a new support chat session using Realtime Database with Firestore sync
  static Future<String> createSupportChatSession() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw 'User not authenticated';

    try {
      // Create chat session in Firestore for basic info storage
      final chatDoc = await _firestore.collection('support_chats').add({
        'userId': user.uid,
        'userName': user.displayName ?? 'User',
        'userEmail': user.email,
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'active',
        'lastMessageAt': FieldValue.serverTimestamp(),
        'isAIChat': true,
        'messageCount': 0,
      });

      final chatId = chatDoc.id;

      // Create chat session in Realtime Database for real-time messaging
      await _realtimeDb.ref('support_chats/$chatId').set({
        'userId': user.uid,
        'userName': user.displayName ?? 'User',
        'userEmail': user.email,
        'createdAt': ServerValue.timestamp,
        'status': 'active',
        'lastMessageAt': ServerValue.timestamp,
        'isAIChat': true,
      });

      // Send welcome message
      await _sendAIMessage(
        chatId,
        "Hello! I'm EvenOut Support Assistant. I'm here to help you with any questions about the EvenOut app. How can I assist you today?",
      );

      return chatId;
    } catch (e) {
      print('Error creating support chat: $e');
      rethrow;
    }
  }

  // Send user message and get AI response
  static Future<void> sendMessage(String chatId, String message) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw 'User not authenticated';

    try {
      // Save user message to Realtime Database
      final messageRef = _realtimeDb.ref('support_chats/$chatId/messages').push();
      await messageRef.set({
        'senderId': user.uid,
        'senderName': user.displayName ?? 'User',
        'message': message,
        'timestamp': ServerValue.timestamp,
        'type': 'user',
      });

      // Update chat last message time in both databases
      await _realtimeDb.ref('support_chats/$chatId').update({
        'lastMessageAt': ServerValue.timestamp,
      });

      await _firestore.collection('support_chats').doc(chatId).update({
        'lastMessageAt': FieldValue.serverTimestamp(),
        'messageCount': FieldValue.increment(1),
      });

      // Get AI response
      await _getAIResponse(chatId, message);
    } catch (e) {
      print('Error sending message: $e');
      rethrow;
    }
  }

  // Get AI response using Firebase AI (Gemini)
  static Future<void> _getAIResponse(String chatId, String userMessage) async {
    if (!_isInitialized || _model == null) {
      await initialize();
    }

    try {
      // Get recent chat history for context (last 10 messages)
      final messagesSnapshot = await _realtimeDb
          .ref('support_chats/$chatId/messages')
          .orderByChild('timestamp')
          .limitToLast(10)
          .once();

      // Build conversation context
      List<Content> conversationHistory = [];

      if (messagesSnapshot.snapshot.value != null) {
        final messagesMap = messagesSnapshot.snapshot.value as Map<dynamic, dynamic>;
        final sortedMessages = messagesMap.entries.toList()
          ..sort((a, b) => (a.value['timestamp'] as int).compareTo(b.value['timestamp'] as int));

        for (var entry in sortedMessages) {
          final messageData = entry.value as Map<dynamic, dynamic>;
          final isUser = messageData['type'] == 'user';
          final messageText = messageData['message'] as String;

          if (isUser) {
            conversationHistory.add(Content.text('User: $messageText'));
          } else {
            conversationHistory.add(Content.text('Assistant: $messageText'));
          }
        }
      }

      // Add current user message
      conversationHistory.add(Content.text('User: $userMessage'));

      // Generate AI response using Firebase AI
      final response = await _model!.generateContent(conversationHistory);
      final aiMessage = response.text ?? 'I apologize, but I\'m having trouble responding right now. Please try again or contact our human support team.';

      // Check if AI suggests closing the chat
      final shouldOfferClosure = _shouldOfferChatClosure(userMessage, aiMessage);
      final finalMessage = shouldOfferClosure
          ? '$aiMessage\n\nIs there anything else I can help you with, or would you like me to close this chat?'
          : aiMessage;

      // Save AI response
      await _sendAIMessage(chatId, finalMessage);
    } catch (e) {
      print('Error getting AI response: $e');
      // Send fallback message
      await _sendAIMessage(
        chatId,
        'I apologize, but I\'m experiencing technical difficulties. Please try again in a moment or contact our human support team for immediate assistance.',
      );
    }
  }

  // Check if chat closure should be offered based on user message and AI response
  static bool _shouldOfferChatClosure(String userMessage, String aiResponse) {
    final message = userMessage.toLowerCase();
    final response = aiResponse.toLowerCase();

    // Offer closure if user expresses satisfaction or completion
    if (message.contains('thank') || message.contains('thanks') ||
        message.contains('that\'s all') || message.contains('no more') ||
        message.contains('perfect') || message.contains('great') ||
        message.contains('solved') || message.contains('fixed')) {
      return true;
    }

    // Offer closure if AI response seems to complete a solution
    if (response.contains('you should now be able') ||
        response.contains('that should resolve') ||
        response.contains('this should fix') ||
        response.contains('you\'re all set')) {
      return true;
    }

    return false;
  }

  // Send AI message to chat using Realtime Database
  static Future<void> _sendAIMessage(String chatId, String message) async {
    try {
      // Save AI message to Realtime Database
      final messageRef = _realtimeDb.ref('support_chats/$chatId/messages').push();
      await messageRef.set({
        'senderId': 'ai_assistant',
        'senderName': 'EvenOut Support',
        'message': message,
        'timestamp': ServerValue.timestamp,
        'type': 'ai',
      });

      // Update chat last message time in both databases
      await _realtimeDb.ref('support_chats/$chatId').update({
        'lastMessageAt': ServerValue.timestamp,
      });

      await _firestore.collection('support_chats').doc(chatId).update({
        'lastMessageAt': FieldValue.serverTimestamp(),
        'messageCount': FieldValue.increment(1),
      });
    } catch (e) {
      print('Error sending AI message: $e');
      rethrow;
    }
  }

  // Get chat messages stream from Realtime Database
  static Stream<DatabaseEvent> getChatMessages(String chatId) {
    return _realtimeDb
        .ref('support_chats/$chatId/messages')
        .orderByChild('timestamp')
        .onValue;
  }

  // Close support chat in both databases
  static Future<void> closeSupportChat(String chatId) async {
    try {
      // Update status in both databases
      await _realtimeDb.ref('support_chats/$chatId').update({
        'status': 'closed',
        'closedAt': ServerValue.timestamp,
      });

      await _firestore.collection('support_chats').doc(chatId).update({
        'status': 'closed',
        'closedAt': FieldValue.serverTimestamp(),
      });

      // Send closing message
      await _sendAIMessage(
        chatId,
        'Thank you for using EvenOut Support! This chat session has been closed. If you need further assistance, feel free to start a new chat anytime.',
      );
    } catch (e) {
      print('Error closing support chat: $e');
      rethrow;
    }
  }
}
