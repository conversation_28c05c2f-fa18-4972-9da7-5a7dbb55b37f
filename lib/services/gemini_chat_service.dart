import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class GeminiChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Initialize Gemini AI (placeholder for now)
  static Future<void> initialize() async {
    try {
      // TODO: Initialize Firebase AI when properly configured
      // For now, using predefined responses
      print('Gemini AI service initialized (placeholder mode)');
    } catch (e) {
      print('Error initializing Gemini AI: $e');
      rethrow;
    }
  }

  // Create a new support chat session
  static Future<String> createSupportChatSession() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw 'User not authenticated';

    try {
      final chatDoc = await _firestore.collection('support_chats').add({
        'userId': user.uid,
        'userName': user.displayName ?? 'User',
        'userEmail': user.email,
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'active',
        'lastMessageAt': FieldValue.serverTimestamp(),
        'isAIChat': true,
      });

      // Send welcome message
      await _sendAIMessage(
        chatDoc.id,
        "Hello! I'm EvenOut Support Assistant. I'm here to help you with any questions about the EvenOut app. How can I assist you today?",
      );

      return chatDoc.id;
    } catch (e) {
      print('Error creating support chat: $e');
      rethrow;
    }
  }

  // Send user message and get AI response
  static Future<void> sendMessage(String chatId, String message) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw 'User not authenticated';

    try {
      // Save user message
      await _firestore
          .collection('support_chats')
          .doc(chatId)
          .collection('messages')
          .add({
        'senderId': user.uid,
        'senderName': user.displayName ?? 'User',
        'message': message,
        'timestamp': FieldValue.serverTimestamp(),
        'type': 'user',
      });

      // Update chat last message time
      await _firestore.collection('support_chats').doc(chatId).update({
        'lastMessageAt': FieldValue.serverTimestamp(),
      });

      // Get AI response
      await _getAIResponse(chatId, message);
    } catch (e) {
      print('Error sending message: $e');
      rethrow;
    }
  }

  // Get AI response using predefined responses (placeholder for Gemini)
  static Future<void> _getAIResponse(String chatId, String userMessage) async {
    try {
      // Simulate AI thinking time
      await Future.delayed(const Duration(seconds: 1));

      // Generate response based on keywords (placeholder logic)
      String aiMessage = _generateResponse(userMessage);

      // Save AI response
      await _sendAIMessage(chatId, aiMessage);
    } catch (e) {
      print('Error getting AI response: $e');
      // Send fallback message
      await _sendAIMessage(
        chatId,
        'I apologize, but I\'m experiencing technical difficulties. Please try again in a moment or contact our human support team for immediate assistance.',
      );
    }
  }

  // Generate response based on user message (placeholder for Gemini AI)
  static String _generateResponse(String userMessage) {
    final message = userMessage.toLowerCase();

    if (message.contains('hello') || message.contains('hi') || message.contains('hey')) {
      return 'Hello! I\'m here to help you with any questions about EvenOut. What can I assist you with today?';
    } else if (message.contains('swap') || message.contains('trade')) {
      return 'I can help you with swapping items! To swap an item, go to the item details page and tap the "Offer to Swap" button. You can then select an item from your collection to offer in exchange.';
    } else if (message.contains('profile') || message.contains('verification')) {
      return 'For profile verification, go to your Profile tab and tap on "Profile Verification". You\'ll need to verify your phone number, email, and upload a government ID for full verification.';
    } else if (message.contains('location') || message.contains('nearby')) {
      return 'EvenOut shows items near your location automatically. Make sure location permissions are enabled in your device settings for the best experience.';
    } else if (message.contains('chat') || message.contains('message')) {
      return 'You can chat with other users when you send a swap request or buy offer. The chat will appear in your Messages tab once the request is sent.';
    } else if (message.contains('problem') || message.contains('issue') || message.contains('bug')) {
      return 'I\'m sorry to hear you\'re experiencing issues. Can you please describe the specific problem you\'re facing? If it\'s a technical issue, you can also report it using the "Report a Bug" option in Help & Support.';
    } else if (message.contains('account') || message.contains('login')) {
      return 'For account-related issues, make sure you\'re using the correct email and password. If you\'ve forgotten your password, use the "Forgot Password" option on the login screen.';
    } else if (message.contains('thank') || message.contains('thanks')) {
      return 'You\'re welcome! I\'m glad I could help. Is there anything else you\'d like to know about EvenOut?';
    } else if (message.contains('bye') || message.contains('goodbye')) {
      return 'Goodbye! Feel free to reach out anytime if you need help with EvenOut. Have a great day!';
    } else {
      return 'I understand you\'re asking about "${userMessage}". While I\'d love to help with that specific question, I recommend contacting our human support team for more detailed assistance. You can reach them through the "Contact Support" option in Help & Support.';
    }
  }

  // Send AI message to chat
  static Future<void> _sendAIMessage(String chatId, String message) async {
    try {
      await _firestore
          .collection('support_chats')
          .doc(chatId)
          .collection('messages')
          .add({
        'senderId': 'ai_assistant',
        'senderName': 'EvenOut Support',
        'message': message,
        'timestamp': FieldValue.serverTimestamp(),
        'type': 'ai',
      });

      // Update chat last message time
      await _firestore.collection('support_chats').doc(chatId).update({
        'lastMessageAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error sending AI message: $e');
      rethrow;
    }
  }

  // Get chat messages stream
  static Stream<QuerySnapshot> getChatMessages(String chatId) {
    return _firestore
        .collection('support_chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: false)
        .snapshots();
  }

  // Close support chat
  static Future<void> closeSupportChat(String chatId) async {
    try {
      await _firestore.collection('support_chats').doc(chatId).update({
        'status': 'closed',
        'closedAt': FieldValue.serverTimestamp(),
      });

      // Send closing message
      await _sendAIMessage(
        chatId,
        'Thank you for using EvenOut Support! This chat session has been closed. If you need further assistance, feel free to start a new chat anytime.',
      );
    } catch (e) {
      print('Error closing support chat: $e');
      rethrow;
    }
  }
}
