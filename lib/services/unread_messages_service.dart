import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'auth_service.dart';

class UnreadMessagesService {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;
  static StreamSubscription<DatabaseEvent>? _subscription;
  static final StreamController<bool> _hasUnreadController = StreamController<bool>.broadcast();

  /// Stream that emits true when there are unread messages, false otherwise
  static Stream<bool> get hasUnreadMessages => _hasUnreadController.stream;

  /// Start listening for unread messages
  static void startListening() {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return;

    // Stop any existing subscription
    stopListening();

    // Listen to all chats where current user is a participant
    _subscription = _database
        .ref('chats')
        .onValue
        .listen((event) {
      _checkForUnreadMessages(currentUser.uid);
    });
  }

  /// Stop listening for unread messages
  static void stopListening() {
    _subscription?.cancel();
    _subscription = null;
  }

  /// Check if current user has any unread messages
  static Future<void> _checkForUnreadMessages(String userId) async {
    try {
      final snapshot = await _database.ref('chats').get();
      
      if (!snapshot.exists || snapshot.value == null) {
        _hasUnreadController.add(false);
        return;
      }

      bool hasUnread = false;
      final chatsData = snapshot.value as Map<dynamic, dynamic>;

      for (final chatEntry in chatsData.entries) {
        final chatData = chatEntry.value as Map<dynamic, dynamic>;
        final participants = List<String>.from(chatData['participants'] ?? []);
        
        // Check if current user is a participant
        if (participants.contains(userId)) {
          final unreadCount = chatData['unreadCount'] as Map<dynamic, dynamic>?;
          final userUnreadCount = unreadCount?[userId] as int? ?? 0;
          
          if (userUnreadCount > 0) {
            hasUnread = true;
            break;
          }
        }
      }

      _hasUnreadController.add(hasUnread);
    } catch (e) {
      print('Error checking unread messages: $e');
      _hasUnreadController.add(false);
    }
  }

  /// Manually trigger a check for unread messages
  static Future<void> checkUnreadMessages() async {
    final currentUser = AuthService.currentUser;
    if (currentUser != null) {
      await _checkForUnreadMessages(currentUser.uid);
    }
  }

  /// Dispose resources
  static void dispose() {
    stopListening();
    _hasUnreadController.close();
  }
}
