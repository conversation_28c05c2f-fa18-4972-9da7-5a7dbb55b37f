import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';
import 'dart:math';
import '../services/auth_service.dart';

class HomeCacheService {
  static const String _cacheKey = 'home_items_cache';
  static const String _lastUpdateKey = 'home_items_last_update';
  static const Duration _cacheValidDuration = Duration(minutes: 30);

  /// Saves items to local cache
  static Future<void> saveItemsToCache(List<Map<String, dynamic>> items) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Convert items to JSON strings
      final List<String> itemsJson = items.map((item) {
        // Convert Timestamp to milliseconds for storage
        final Map<String, dynamic> itemCopy = Map.from(item);
        if (itemCopy['createdAt'] is Timestamp) {
          itemCopy['createdAt'] = (itemCopy['createdAt'] as Timestamp).millisecondsSinceEpoch;
        }
        if (itemCopy['updatedAt'] is Timestamp) {
          itemCopy['updatedAt'] = (itemCopy['updatedAt'] as Timestamp).millisecondsSinceEpoch;
        }
        if (itemCopy['swapEndDate'] is Timestamp) {
          itemCopy['swapEndDate'] = (itemCopy['swapEndDate'] as Timestamp).millisecondsSinceEpoch;
        }
        return jsonEncode(itemCopy);
      }).toList();
      
      await prefs.setStringList(_cacheKey, itemsJson);
      await prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('Error saving items to cache: $e');
    }
  }

  /// Gets items from local cache
  static Future<List<Map<String, dynamic>>> getItemsFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String>? itemsJson = prefs.getStringList(_cacheKey);
      
      if (itemsJson == null) return [];
      
      return itemsJson.map((itemJson) {
        final Map<String, dynamic> item = Map.from(jsonDecode(itemJson));
        
        // Convert milliseconds back to Timestamp
        if (item['createdAt'] is int) {
          item['createdAt'] = Timestamp.fromMillisecondsSinceEpoch(item['createdAt']);
        }
        if (item['updatedAt'] is int) {
          item['updatedAt'] = Timestamp.fromMillisecondsSinceEpoch(item['updatedAt']);
        }
        if (item['swapEndDate'] is int) {
          item['swapEndDate'] = Timestamp.fromMillisecondsSinceEpoch(item['swapEndDate']);
        }
        
        return item;
      }).toList();
    } catch (e) {
      print('Error getting items from cache: $e');
      return [];
    }
  }

  /// Checks if cache is still valid
  static Future<bool> isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final int? lastUpdate = prefs.getInt(_lastUpdateKey);
      
      if (lastUpdate == null) return false;
      
      final DateTime lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdate);
      final DateTime now = DateTime.now();
      
      return now.difference(lastUpdateTime) < _cacheValidDuration;
    } catch (e) {
      return false;
    }
  }

  /// Clears the cache
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_lastUpdateKey);
    } catch (e) {
      print('Error clearing cache: $e');
    }
  }

  /// Fetches fresh items from Firestore
  static Future<List<Map<String, dynamic>>> fetchFreshItems({
    double? userLatitude,
    double? userLongitude,
  }) async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return [];

      // Fetch all active items except user's own items
      final querySnapshot = await FirebaseFirestore.instance
          .collection('items')
          .where('isActive', isEqualTo: true)
          .where('userId', isNotEqualTo: user.uid)
          .orderBy('createdAt', descending: true)
          .get();

      List<Map<String, dynamic>> items = [];

      for (var doc in querySnapshot.docs) {
        final data = doc.data();
        final itemLatitude = data['latitude'] as double?;
        final itemLongitude = data['longitude'] as double?;

        // Calculate distance if both user and item have coordinates
        double distance = double.infinity;
        if (userLatitude != null &&
            userLongitude != null &&
            itemLatitude != null &&
            itemLongitude != null) {
          distance = _calculateDistance(
            userLatitude,
            userLongitude,
            itemLatitude,
            itemLongitude,
          );
        }

        // Add distance and document ID to item data
        data['distance'] = distance;
        data['docId'] = doc.id;
        items.add(data);
      }

      // Sort items by distance (closest first) if location is available
      if (userLatitude != null && userLongitude != null) {
        items.sort(
          (a, b) => (a['distance'] as double).compareTo(b['distance'] as double),
        );
      }

      return items;
    } catch (e) {
      print('Error fetching fresh items: $e');
      return [];
    }
  }

  /// Calculates distance between two coordinates in kilometers
  static double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    
    final double a = 
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) * cos(_degreesToRadians(lat2)) *
        sin(dLon / 2) * sin(dLon / 2);
    
    final double c = 2 * asin(sqrt(a));
    
    return earthRadius * c;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Gets cached items and starts background refresh
  /// Returns cached items immediately and fresh items via callback
  static Future<List<Map<String, dynamic>>> getItemsWithBackgroundRefresh({
    double? userLatitude,
    double? userLongitude,
    Function(List<Map<String, dynamic>>)? onFreshData,
  }) async {
    // Get cached items first
    List<Map<String, dynamic>> cachedItems = await getItemsFromCache();
    
    // Start background refresh
    _refreshInBackground(
      userLatitude: userLatitude,
      userLongitude: userLongitude,
      onFreshData: onFreshData,
    );
    
    return cachedItems;
  }

  /// Refreshes data in background and calls callback with fresh data
  static Future<void> _refreshInBackground({
    double? userLatitude,
    double? userLongitude,
    Function(List<Map<String, dynamic>>)? onFreshData,
  }) async {
    try {
      // Fetch fresh data
      final freshItems = await fetchFreshItems(
        userLatitude: userLatitude,
        userLongitude: userLongitude,
      );
      
      // Save to cache
      await saveItemsToCache(freshItems);
      
      // Notify with fresh data
      if (onFreshData != null) {
        onFreshData(freshItems);
      }
    } catch (e) {
      print('Error in background refresh: $e');
    }
  }

  /// Forces a refresh and returns fresh data
  static Future<List<Map<String, dynamic>>> forceRefresh({
    double? userLatitude,
    double? userLongitude,
  }) async {
    final freshItems = await fetchFreshItems(
      userLatitude: userLatitude,
      userLongitude: userLongitude,
    );
    
    await saveItemsToCache(freshItems);
    return freshItems;
  }
}
