import 'package:firebase_auth/firebase_auth.dart';
import 'dart:math';

class OTPService {
  // Placeholder for OTP storage (in production, use secure backend)
  static final Map<String, Map<String, dynamic>> _otpStorage = {};

  /// Generates a 6-digit OTP
  static String _generateOTP() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  /// Validates OTP format (6 digits)
  static bool isValidOTP(String otp) {
    if (otp.length != 6) return false;
    return RegExp(r'^\d{6}$').hasMatch(otp);
  }

  /// Sends OTP to the specified email address
  ///
  /// [email] - The email address to send OTP to
  /// [userName] - Optional user name for personalization
  ///
  /// Returns a Map with success status, message, and expiration time
  ///
  /// Throws an exception if sending fails
  static Future<Map<String, dynamic>> sendEmailOTP({
    required String email,
    String? userName,
  }) async {
    try {
      // Generate a 6-digit OTP
      final otp = _generateOTP();
      final expiresAt = DateTime.now().add(const Duration(minutes: 10));

      // Store OTP temporarily (in production, use secure backend)
      _otpStorage[email.toLowerCase().trim()] = {
        'otp': otp,
        'expiresAt': expiresAt.millisecondsSinceEpoch,
        'userName': userName ?? 'User',
      };

      // In production, send actual email here
      // For now, we'll simulate success and log the OTP for testing
      print('OTP for $email: $otp (expires at $expiresAt)');

      return {
        'success': true,
        'message': 'OTP sent successfully to $email',
        'expiresAt': expiresAt.millisecondsSinceEpoch,
        'testOTP': otp, // Remove this in production
      };
    } catch (e) {
      throw Exception('Failed to send OTP: ${e.toString()}');
    }
  }

  /// Verifies the OTP for the specified email
  ///
  /// [email] - The email address to verify OTP for
  /// [otp] - The OTP to verify
  ///
  /// Returns a Map with success status and message
  ///
  /// Throws an exception if verification fails
  static Future<Map<String, dynamic>> verifyEmailOTP({
    required String email,
    required String otp,
  }) async {
    try {
      final emailKey = email.toLowerCase().trim();
      final storedData = _otpStorage[emailKey];

      if (storedData == null) {
        throw Exception('No OTP found for this email. Please request a new OTP.');
      }

      final storedOTP = storedData['otp'] as String;
      final expiresAt = storedData['expiresAt'] as int;
      final now = DateTime.now().millisecondsSinceEpoch;

      if (now > expiresAt) {
        _otpStorage.remove(emailKey);
        throw Exception('OTP has expired. Please request a new OTP.');
      }

      if (otp.trim() != storedOTP) {
        throw Exception('Invalid OTP. Please check and try again.');
      }

      // OTP is valid, remove it from storage
      _otpStorage.remove(emailKey);

      return {
        'success': true,
        'message': 'OTP verified successfully',
      };
    } catch (e) {
      throw Exception('Failed to verify OTP: ${e.toString()}');
    }
  }

  // Placeholder methods for other email functionalities
  // In production, these would use a proper email service

  static Future<Map<String, dynamic>> sendWelcomeEmail({
    required String email,
    String? userName,
  }) async {
    print('Welcome email would be sent to $email');
    return {'success': true, 'message': 'Welcome email sent'};
  }

  static Future<Map<String, dynamic>> sendEmailVerifiedNotification({
    required String email,
    String? userName,
  }) async {
    print('Email verified notification would be sent to $email');
    return {'success': true, 'message': 'Email verified notification sent'};
  }

  static Future<Map<String, dynamic>> sendDocumentSubmittedNotification({
    required String email,
    String? userName,
    String? documentType,
  }) async {
    print('Document submitted notification would be sent to $email');
    return {'success': true, 'message': 'Document submitted notification sent'};
  }

  static Future<Map<String, dynamic>> sendDocumentVerifiedNotification({
    required String email,
    String? userName,
    String? documentType,
  }) async {
    print('Document verified notification would be sent to $email');
    return {'success': true, 'message': 'Document verified notification sent'};
  }

  static Future<Map<String, dynamic>> sendAccountDeletionOTP({
    required String email,
    String? userName,
  }) async {
    // Use the same OTP generation logic for account deletion
    return sendEmailOTP(email: email, userName: userName);
  }

  static Future<Map<String, dynamic>> verifyAccountDeletionOTP({
    required String email,
    required String otp,
  }) async {
    // Use the same OTP verification logic for account deletion
    return verifyEmailOTP(email: email, otp: otp);
  }

  static Future<Map<String, dynamic>> sendSwapAcceptedEmails({
    required String ownerEmail,
    required String requesterEmail,
    String? ownerName,
    String? requesterName,
    String? ownerItemName,
    String? requesterItemName,
  }) async {
    print('Swap accepted emails would be sent to $ownerEmail and $requesterEmail');
    return {'success': true, 'message': 'Swap accepted emails sent'};
  }

  static Future<Map<String, dynamic>> sendSwapRejectedEmails({
    required String ownerEmail,
    required String requesterEmail,
    String? ownerName,
    String? requesterName,
    required String itemName,
  }) async {
    print('Swap rejected emails would be sent to $ownerEmail and $requesterEmail');
    return {'success': true, 'message': 'Swap rejected emails sent'};
  }
}
