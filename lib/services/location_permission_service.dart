import 'package:geolocator/geolocator.dart';

class LocationPermissionService {
  static final LocationPermissionService _instance = LocationPermissionService._internal();
  factory LocationPermissionService() => _instance;
  LocationPermissionService._internal();

  /// Check if location permission is granted
  static Future<bool> isLocationPermissionGranted() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      return permission == LocationPermission.whileInUse || 
             permission == LocationPermission.always;
    } catch (e) {
      return false;
    }
  }

  /// Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      return false;
    }
  }

  /// Check if location is fully available (service enabled + permission granted)
  static Future<bool> isLocationAvailable() async {
    try {
      bool serviceEnabled = await isLocationServiceEnabled();
      bool permissionGranted = await isLocationPermissionGranted();
      return serviceEnabled && permissionGranted;
    } catch (e) {
      return false;
    }
  }

  /// Get detailed location permission status
  static Future<LocationPermissionStatus> getLocationPermissionStatus() async {
    try {
      bool serviceEnabled = await isLocationServiceEnabled();
      LocationPermission permission = await Geolocator.checkPermission();

      if (!serviceEnabled) {
        return LocationPermissionStatus.serviceDisabled;
      }

      switch (permission) {
        case LocationPermission.denied:
          return LocationPermissionStatus.denied;
        case LocationPermission.deniedForever:
          return LocationPermissionStatus.deniedForever;
        case LocationPermission.whileInUse:
        case LocationPermission.always:
          return LocationPermissionStatus.granted;
        default:
          return LocationPermissionStatus.denied;
      }
    } catch (e) {
      return LocationPermissionStatus.error;
    }
  }

  /// Request location permission
  static Future<LocationPermissionResult> requestLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationPermissionResult(
          status: LocationPermissionStatus.serviceDisabled,
          message: 'Location services are disabled. Please enable them in settings.',
        );
      }

      // Check current permission
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.deniedForever) {
        return LocationPermissionResult(
          status: LocationPermissionStatus.deniedForever,
          message: 'Location permission is permanently denied. Please enable it in app settings.',
        );
      }

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.whileInUse || 
          permission == LocationPermission.always) {
        return LocationPermissionResult(
          status: LocationPermissionStatus.granted,
          message: 'Location permission granted successfully.',
        );
      } else if (permission == LocationPermission.deniedForever) {
        return LocationPermissionResult(
          status: LocationPermissionStatus.deniedForever,
          message: 'Location permission is permanently denied. Please enable it in app settings.',
        );
      } else {
        return LocationPermissionResult(
          status: LocationPermissionStatus.denied,
          message: 'Location permission was denied.',
        );
      }
    } catch (e) {
      return LocationPermissionResult(
        status: LocationPermissionStatus.error,
        message: 'Failed to request location permission: $e',
      );
    }
  }

  /// Open location settings
  static Future<void> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
    } catch (e) {
      // Handle error silently
    }
  }

  /// Open app settings
  static Future<void> openAppSettings() async {
    try {
      await Geolocator.openAppSettings();
    } catch (e) {
      // Handle error silently
    }
  }
}

/// Enum for location permission status
enum LocationPermissionStatus {
  granted,
  denied,
  deniedForever,
  serviceDisabled,
  error,
}

/// Class to hold location permission result
class LocationPermissionResult {
  final LocationPermissionStatus status;
  final String message;

  const LocationPermissionResult({
    required this.status,
    required this.message,
  });

  bool get isGranted => status == LocationPermissionStatus.granted;
  bool get isDenied => status == LocationPermissionStatus.denied;
  bool get isDeniedForever => status == LocationPermissionStatus.deniedForever;
  bool get isServiceDisabled => status == LocationPermissionStatus.serviceDisabled;
  bool get hasError => status == LocationPermissionStatus.error;
}
