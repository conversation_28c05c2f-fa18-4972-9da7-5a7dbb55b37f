import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';
import '../services/auth_service.dart';

class SearchService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _searchHistoryKey = 'search_history';
  static const int _maxHistoryItems = 20;

  /// Performs a comprehensive search across all items
  ///
  /// [query] - The search term
  /// [category] - Optional category filter
  /// [userLatitude] - User's latitude for distance calculation
  /// [userLongitude] - User's longitude for distance calculation
  ///
  /// Returns a list of matching items sorted by relevance and distance
  static Future<List<Map<String, dynamic>>> searchItems({
    required String query,
    String? category,
    double? userLatitude,
    double? userLongitude,
  }) async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return [];

      // Get all active items except user's own items
      Query itemsQuery = _firestore
          .collection('items')
          .where('isActive', isEqualTo: true)
          .where('userId', isNotEqualTo: user.uid);

      // Add category filter if specified
      if (category != null && category.isNotEmpty) {
        itemsQuery = itemsQuery.where('category', isEqualTo: category);
      }

      final querySnapshot = await itemsQuery.get();
      List<Map<String, dynamic>> items = [];

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        data['docId'] = doc.id;

        // Calculate distance if coordinates are available
        if (userLatitude != null && userLongitude != null) {
          final itemLatitude = data['latitude'] as double?;
          final itemLongitude = data['longitude'] as double?;

          if (itemLatitude != null && itemLongitude != null) {
            data['distance'] = _calculateDistance(
              userLatitude,
              userLongitude,
              itemLatitude,
              itemLongitude,
            );
          } else {
            data['distance'] = double.infinity;
          }
        } else {
          data['distance'] = double.infinity;
        }

        items.add(data);
      }

      // Filter items based on search query with fuzzy matching
      if (query.isNotEmpty) {
        items = _filterItemsByQuery(items, query);
      }

      // Sort by relevance score and then by distance
      items.sort((a, b) {
        final scoreA = a['relevanceScore'] as double? ?? 0.0;
        final scoreB = b['relevanceScore'] as double? ?? 0.0;

        // First sort by relevance score (higher is better)
        final scoreComparison = scoreB.compareTo(scoreA);
        if (scoreComparison != 0) return scoreComparison;

        // Then sort by distance (closer is better)
        final distanceA = a['distance'] as double? ?? double.infinity;
        final distanceB = b['distance'] as double? ?? double.infinity;
        return distanceA.compareTo(distanceB);
      });

      return items;
    } catch (e) {
      print('Error searching items: $e');
      return [];
    }
  }

  /// Filters items based on search query with fuzzy matching
  static List<Map<String, dynamic>> _filterItemsByQuery(
    List<Map<String, dynamic>> items,
    String query,
  ) {
    final queryLower = query.toLowerCase();
    final queryWords = queryLower.split(' ').where((word) => word.isNotEmpty).toList();

    List<Map<String, dynamic>> matchingItems = [];

    for (var item in items) {
      double relevanceScore = 0.0;

      // Get searchable text fields
      final itemName = (item['itemName'] as String? ?? '').toLowerCase();
      final description = (item['description'] as String? ?? '').toLowerCase();
      final category = (item['category'] as String? ?? '').toLowerCase();
      final condition = (item['condition'] as String? ?? '').toLowerCase();
      final conditionDescription = (item['conditionDescription'] as String? ?? '').toLowerCase();

      // Combine all searchable text
      final searchableText = '$itemName $description $category $condition $conditionDescription';

      // Calculate relevance score
      for (String word in queryWords) {
        // Exact match in item name (highest score)
        if (itemName.contains(word)) {
          relevanceScore += 10.0;
          if (itemName.startsWith(word)) {
            relevanceScore += 5.0; // Bonus for starting with the word
          }
        }

        // Exact match in category
        if (category.contains(word)) {
          relevanceScore += 8.0;
        }

        // Exact match in description
        if (description.contains(word)) {
          relevanceScore += 5.0;
        }

        // Exact match in condition
        if (condition.contains(word)) {
          relevanceScore += 3.0;
        }

        // Exact match in condition description
        if (conditionDescription.contains(word)) {
          relevanceScore += 2.0;
        }

        // Fuzzy matching (partial matches)
        relevanceScore += _calculateFuzzyScore(searchableText, word);
      }

      // Full query match bonus
      if (searchableText.contains(queryLower)) {
        relevanceScore += 15.0;
      }

      // Include item if relevance score is above threshold (10% match)
      if (relevanceScore >= 1.0) {
        item['relevanceScore'] = relevanceScore;
        matchingItems.add(item);
      }
    }

    return matchingItems;
  }

  /// Calculates fuzzy matching score for partial matches
  static double _calculateFuzzyScore(String text, String word) {
    if (word.length < 3) return 0.0; // Skip very short words for fuzzy matching

    double score = 0.0;

    // Check for partial matches (at least 70% of the word)
    final minMatchLength = (word.length * 0.7).ceil();

    for (int i = 0; i <= word.length - minMatchLength; i++) {
      final substring = word.substring(i, i + minMatchLength);
      if (text.contains(substring)) {
        score += 1.0 * (substring.length / word.length);
      }
    }

    return score;
  }

  /// Calculates distance between two coordinates in kilometers
  static double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) * cos(_degreesToRadians(lat2)) *
        sin(dLon / 2) * sin(dLon / 2);

    final double c = 2 * asin(sqrt(a));

    return earthRadius * c;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }

  /// Saves search query to local cache and Firestore
  static Future<void> saveSearchHistory(String query) async {
    if (query.trim().isEmpty) return;

    try {
      // Save to local cache
      await _saveToLocalCache(query.trim());

      // Save to Firestore
      await _saveToFirestore(query.trim());
    } catch (e) {
      print('Error saving search history: $e');
    }
  }

  /// Saves search query to local SharedPreferences
  static Future<void> _saveToLocalCache(String query) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> history = prefs.getStringList(_searchHistoryKey) ?? [];

    // Remove if already exists to avoid duplicates
    history.remove(query);

    // Add to beginning
    history.insert(0, query);

    // Limit history size
    if (history.length > _maxHistoryItems) {
      history = history.take(_maxHistoryItems).toList();
    }

    await prefs.setStringList(_searchHistoryKey, history);
  }

  /// Saves search query to Firestore
  static Future<void> _saveToFirestore(String query) async {
    final user = AuthService.currentUser;
    if (user == null) return;

    try {
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchHistory')
          .add({
        'query': query,
        'timestamp': FieldValue.serverTimestamp(),
      });

      // Clean up old history (keep only last 50 searches)
      final allSearches = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchHistory')
          .orderBy('timestamp', descending: true)
          .get();

      if (allSearches.docs.length > 50) {
        final oldSearches = allSearches.docs.skip(50);
        for (var doc in oldSearches) {
          await doc.reference.delete();
        }
      }
    } catch (e) {
      print('Error saving to Firestore: $e');
    }
  }

  /// Gets search history from local cache
  static Future<List<String>> getLocalSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(_searchHistoryKey) ?? [];
    } catch (e) {
      print('Error getting local search history: $e');
      return [];
    }
  }

  /// Gets search history from Firestore
  static Future<List<String>> getFirestoreSearchHistory() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return [];

      final querySnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchHistory')
          .orderBy('timestamp', descending: true)
          .limit(20)
          .get();

      return querySnapshot.docs
          .map((doc) => doc.data()['query'] as String)
          .toList();
    } catch (e) {
      print('Error getting Firestore search history: $e');
      return [];
    }
  }

  /// Gets combined search history (local + Firestore, deduplicated)
  static Future<List<String>> getSearchHistory() async {
    try {
      final localHistory = await getLocalSearchHistory();
      final firestoreHistory = await getFirestoreSearchHistory();

      // Combine and deduplicate while preserving order
      final Set<String> seen = {};
      final List<String> combined = [];

      // Add local history first (most recent)
      for (String query in localHistory) {
        if (seen.add(query)) {
          combined.add(query);
        }
      }

      // Add Firestore history
      for (String query in firestoreHistory) {
        if (seen.add(query)) {
          combined.add(query);
        }
      }

      return combined.take(_maxHistoryItems).toList();
    } catch (e) {
      print('Error getting combined search history: $e');
      return [];
    }
  }

  /// Clears all search history
  static Future<void> clearSearchHistory() async {
    try {
      // Clear local cache
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_searchHistoryKey);

      // Clear Firestore
      final user = AuthService.currentUser;
      if (user != null) {
        final querySnapshot = await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('searchHistory')
            .get();

        for (var doc in querySnapshot.docs) {
          await doc.reference.delete();
        }
      }
    } catch (e) {
      print('Error clearing search history: $e');
    }
  }

  /// Maps display category names to database category names
  static String mapCategoryToDatabase(String displayCategory) {
    switch (displayCategory) {
      case 'Electronics':
        return 'Electronics';
      case 'Clothing':
        return 'Clothing & Fashion';
      case 'Books':
        return 'Books & Media';
      case 'Sports':
        return 'Sports & Outdoors';
      case 'Home & Garden':
        return 'Home & Garden';
      case 'Toys':
        return 'Toys & Games';
      case 'Automotive':
        return 'Automotive';
      case 'Health':
        return 'Health & Beauty';
      case 'Collectibles':
        return 'Collectibles';
      case 'Other':
        return 'Other';
      default:
        return displayCategory;
    }
  }
}
