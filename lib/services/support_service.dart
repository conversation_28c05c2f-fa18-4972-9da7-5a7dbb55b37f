import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import 'package:url_launcher/url_launcher.dart';

class SupportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final Logger _logger = Logger();
  static bool _isLogging = false;
  static final List<String> _logBuffer = [];

  // Contact Support - Open email app with pre-filled details
  static Future<void> contactSupport() async {
    const String email = '<EMAIL>';
    const String subject = 'Support Request - EvenOut App';
    final String body = '''
Hello EvenOut Support Team,

I need assistance with:

[Please describe your issue here]

App Version: 1.0.0
User ID: ${FirebaseAuth.instance.currentUser?.uid ?? 'Not logged in'}
Date: ${DateTime.now().toIso8601String()}

Thank you for your help!

Best regards,
${FirebaseAuth.instance.currentUser?.displayName ?? 'EvenOut User'}
    ''';

    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        throw 'Could not launch email app';
      }
    } catch (e) {
      _logger.e('Error launching email: $e');
      rethrow;
    }
  }

  // Call Support - Open dialer with pre-dialed number
  static Future<void> callSupport() async {
    const String phoneNumber = '+1234567890'; // Replace with actual support number
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        throw 'Could not launch dialer';
      }
    } catch (e) {
      _logger.e('Error launching dialer: $e');
      rethrow;
    }
  }

  // Start logging for bug reports
  static void startLogging() {
    _isLogging = true;
    _logBuffer.clear();
    _logger.i('Bug logging started');
    _logBuffer.add('${DateTime.now().toIso8601String()}: Bug logging started');
  }

  // Stop logging and submit bug report
  static Future<void> submitBugReport({
    required String description,
    required String stepsToReproduce,
    required String expectedBehavior,
    required String actualBehavior,
  }) async {
    _isLogging = false;
    _logBuffer.add('${DateTime.now().toIso8601String()}: Bug logging stopped');

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw 'User not authenticated';

    try {
      await _firestore.collection('bug_reports').add({
        'userId': user.uid,
        'userEmail': user.email,
        'userName': user.displayName,
        'description': description,
        'stepsToReproduce': stepsToReproduce,
        'expectedBehavior': expectedBehavior,
        'actualBehavior': actualBehavior,
        'logs': _logBuffer,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'open',
        'appVersion': '1.0.0',
        'platform': 'mobile',
      });

      _logBuffer.clear();
      _logger.i('Bug report submitted successfully');
    } catch (e) {
      _logger.e('Error submitting bug report: $e');
      rethrow;
    }
  }

  // Submit feedback
  static Future<void> submitFeedback({
    required String type,
    required String category,
    required String message,
    int? rating,
  }) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw 'User not authenticated';

    try {
      await _firestore.collection('feedback').add({
        'userId': user.uid,
        'userEmail': user.email,
        'userName': user.displayName,
        'type': type,
        'category': category,
        'message': message,
        'rating': rating,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'new',
        'appVersion': '1.0.0',
        'platform': 'mobile',
      });

      _logger.i('Feedback submitted successfully');
    } catch (e) {
      _logger.e('Error submitting feedback: $e');
      rethrow;
    }
  }

  // Add log entry (called throughout the app when logging is active)
  static void addLogEntry(String message) {
    if (_isLogging) {
      _logBuffer.add('${DateTime.now().toIso8601String()}: $message');
      _logger.d(message);
    }
  }

  // Check if logging is active
  static bool get isLogging => _isLogging;

  // Get current log count
  static int get logCount => _logBuffer.length;
}
