import 'package:flutter/material.dart';
import '../themes/app_theme.dart';

/// Standardized divider widget for consistent thickness across the app
class AppDivider extends StatelessWidget {
  final double? height;
  final double? thickness;
  final Color? color;
  final double? indent;
  final double? endIndent;

  const AppDivider({
    super.key,
    this.height = 1,
    this.thickness = 0.5, // Standardized thickness
    this.color,
    this.indent = 16,
    this.endIndent = 16,
  });

  /// Creates a divider with no indentation (full width)
  const AppDivider.fullWidth({
    super.key,
    this.height = 1,
    this.thickness = 0.5,
    this.color,
  }) : indent = 0, endIndent = 0;

  /// Creates a divider with custom indentation
  const AppDivider.withIndent({
    super.key,
    this.height = 1,
    this.thickness = 0.5,
    this.color,
    required this.indent,
    required this.endIndent,
  });

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: height,
      thickness: thickness,
      color: color ?? AppTheme.getBorderColor(context),
      indent: indent,
      endIndent: endIndent,
    );
  }
}
