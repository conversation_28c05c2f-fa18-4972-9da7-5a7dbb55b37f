import 'package:cloud_firestore/cloud_firestore.dart';

class ChatRoom {
  final String chatId;
  final List<String> participants;
  final String? swapRequestId;
  final String? targetItemId;
  final String? offeredItemId;
  final String requestType; // 'swap' or 'buy'
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? lastMessage;
  final String? lastMessageSenderId;
  final DateTime? lastMessageTime;
  final Map<String, int> unreadCount;
  final bool isActive;

  ChatRoom({
    required this.chatId,
    required this.participants,
    this.swapRequestId,
    this.targetItemId,
    this.offeredItemId,
    required this.requestType,
    required this.createdAt,
    required this.updatedAt,
    this.lastMessage,
    this.lastMessageSenderId,
    this.lastMessageTime,
    required this.unreadCount,
    this.isActive = true,
  });

  factory ChatRoom.fromMap(Map<String, dynamic> map, String chatId) {
    return ChatRoom(
      chatId: chatId,
      participants: List<String>.from(map['participants'] ?? []),
      swapRequestId: map['swapRequestId'],
      targetItemId: map['targetItemId'],
      offeredItemId: map['offeredItemId'],
      requestType: map['requestType'] ?? 'buy',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
      lastMessage: map['lastMessage'],
      lastMessageSenderId: map['lastMessageSenderId'],
      lastMessageTime: map['lastMessageTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastMessageTime'])
          : null,
      unreadCount: Map<String, int>.from(map['unreadCount'] ?? {}),
      isActive: map['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'participants': participants,
      'swapRequestId': swapRequestId,
      'targetItemId': targetItemId,
      'offeredItemId': offeredItemId,
      'requestType': requestType,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'lastMessage': lastMessage,
      'lastMessageSenderId': lastMessageSenderId,
      'lastMessageTime': lastMessageTime?.millisecondsSinceEpoch,
      'unreadCount': unreadCount,
      'isActive': isActive,
    };
  }

  ChatRoom copyWith({
    String? chatId,
    List<String>? participants,
    String? swapRequestId,
    String? targetItemId,
    String? offeredItemId,
    String? requestType,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? lastMessage,
    String? lastMessageSenderId,
    DateTime? lastMessageTime,
    Map<String, int>? unreadCount,
    bool? isActive,
  }) {
    return ChatRoom(
      chatId: chatId ?? this.chatId,
      participants: participants ?? this.participants,
      swapRequestId: swapRequestId ?? this.swapRequestId,
      targetItemId: targetItemId ?? this.targetItemId,
      offeredItemId: offeredItemId ?? this.offeredItemId,
      requestType: requestType ?? this.requestType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCount: unreadCount ?? this.unreadCount,
      isActive: isActive ?? this.isActive,
    );
  }
}

class ChatMessage {
  final String messageId;
  final String chatId;
  final String senderId;
  final String message;
  final MessageType type;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.messageId,
    required this.chatId,
    required this.senderId,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.metadata,
  });

  factory ChatMessage.fromMap(Map<String, dynamic> map, String messageId) {
    return ChatMessage(
      messageId: messageId,
      chatId: map['chatId'] ?? '',
      senderId: map['senderId'] ?? '',
      message: map['message'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.toString().split('.').last == map['type'],
        orElse: () => MessageType.text,
      ),
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
      isRead: map['isRead'] ?? false,
      metadata: map['metadata'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'chatId': chatId,
      'senderId': senderId,
      'message': message,
      'type': type.toString().split('.').last,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isRead': isRead,
      'metadata': metadata,
    };
  }

  ChatMessage copyWith({
    String? messageId,
    String? chatId,
    String? senderId,
    String? message,
    MessageType? type,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      messageId: messageId ?? this.messageId,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      metadata: metadata ?? this.metadata,
    );
  }
}

enum MessageType {
  text,
  swapRequest,
  swapAccepted,
  swapRejected,
  system,
  image,
}

class ChatParticipant {
  final String userId;
  final String name;
  final String email;
  final String? profileImageUrl;
  final bool isOnline;
  final DateTime? lastSeen;

  ChatParticipant({
    required this.userId,
    required this.name,
    required this.email,
    this.profileImageUrl,
    this.isOnline = false,
    this.lastSeen,
  });

  factory ChatParticipant.fromMap(Map<String, dynamic> map) {
    return ChatParticipant(
      userId: map['userId'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      profileImageUrl: map['profileImageUrl'],
      isOnline: map['isOnline'] ?? false,
      lastSeen: map['lastSeen'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastSeen'])
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'name': name,
      'email': email,
      'profileImageUrl': profileImageUrl,
      'isOnline': isOnline,
      'lastSeen': lastSeen?.millisecondsSinceEpoch,
    };
  }
}

class SwapRequestData {
  final String requestId;
  final String requesterId;
  final String ownerId;
  final String targetItemId;
  final String? offeredItemId;
  final String requestType;
  final String status;
  final String? message;
  final DateTime createdAt;

  SwapRequestData({
    required this.requestId,
    required this.requesterId,
    required this.ownerId,
    required this.targetItemId,
    this.offeredItemId,
    required this.requestType,
    required this.status,
    this.message,
    required this.createdAt,
  });

  factory SwapRequestData.fromMap(Map<String, dynamic> map, String requestId) {
    return SwapRequestData(
      requestId: requestId,
      requesterId: map['requesterId'] ?? '',
      ownerId: map['ownerId'] ?? '',
      targetItemId: map['targetItemId'] ?? '',
      offeredItemId: map['offeredItemId'],
      requestType: map['requestType'] ?? 'buy',
      status: map['status'] ?? 'pending',
      message: map['message'],
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }
}
