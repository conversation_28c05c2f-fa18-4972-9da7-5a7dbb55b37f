# Smart Location & List Management Setup

This document explains the privacy-focused location tracking solution implemented for automatic item updates.

## 🎯 Overview

The system provides:
- **Foreground location tracking** when app is in use
- **Periodic location updates** every 1 hour for notifications
- **Smart list management** with Riverpod state management
- **Automatic item updates** when location changes
- **Privacy-focused approach** - no continuous background tracking

## 📦 Dependencies Used

```yaml
dependencies:
  flutter_riverpod: ^2.5.1
  geolocator: ^12.0.0
  geocoding: ^3.0.0
```

## 🏗️ Architecture

### 1. **LocationService** (`lib/services/location_service.dart`)
- Handles background location tracking
- Manages location permissions
- Provides location stream for real-time updates
- Caches location data in SharedPreferences

### 2. **ItemsProvider** (`lib/providers/items_provider.dart`)
- Manages items state using Riverpod
- Listens to location updates
- Merges new items with existing list
- Handles background refresh timer

### 3. **NewItemsNotification** (`lib/widgets/new_items_notification.dart`)
- Shows "New Items Added" pill notification
- Animates in/out smoothly
- Scrolls to top when tapped
- Auto-hides after 5 seconds

## 🚀 Key Features

### **Privacy-Focused Location Tracking**
- **Foreground tracking**: Real-time location when app is active
- **Periodic updates**: Every 1 hour when app is in background
- **No continuous tracking**: Respects user privacy
- **Battery optimized**: Minimal battery usage
- **Permission compliant**: Uses standard location permissions

### **Smart List Management**
- Only adds truly new items to the list
- Maintains sort order by distance
- Preserves scroll position
- Shows notification for new items

### **User Experience**
- No full list refresh (Instagram-style)
- Smooth animations for new items
- Background updates without interruption
- Clear visual feedback

## 📱 Usage

### **Home Screen Integration**
The home screen now uses the new provider system:

```dart
class HomePageScreen extends ConsumerStatefulWidget {
  // Uses Riverpod for state management
}

// Watch items state
final itemsState = ref.watch(itemsProvider);

// Access items list
final items = itemsState.items;

// Check for new items
if (itemsState.hasNewItems) {
  // Show notification
}
```

### **Manual Refresh**
```dart
// Trigger manual refresh
await ref.read(itemsProvider.notifier).refreshItems();
```

### **Clear New Items Notification**
```dart
// Clear the notification
ref.read(itemsProvider.notifier).clearNewItemsNotification();
```

## 🔧 Configuration

### **Location Update Frequency**
```dart
// In LocationService
// Foreground: Real-time location updates when app is active
// Background: Periodic updates every 1 hour
_periodicLocationTimer = Timer.periodic(const Duration(hours: 1), (timer) async {
  await _getCurrentLocation();
});
```

### **App Lifecycle Management**
```dart
// In main.dart
void didChangeAppLifecycleState(AppLifecycleState state) {
  final locationService = LocationService();

  switch (state) {
    case AppLifecycleState.resumed:
      locationService.setAppState(isInForeground: true);
      break;
    case AppLifecycleState.paused:
    case AppLifecycleState.inactive:
    case AppLifecycleState.detached:
      locationService.setAppState(isInForeground: false);
      break;
  }
}
```

### **Background Refresh Timer**
```dart
// In ItemsProvider
Timer.periodic(const Duration(minutes: 2), (timer) {
  _backgroundRefresh();
});
```

### **Notification Auto-Hide**
```dart
// In NewItemsNotification
Future.delayed(const Duration(seconds: 5), () {
  _hideNotification();
});
```

## 📋 Implementation Steps

### 1. **Install Dependencies**
```bash
flutter pub get
```

### 2. **Update Main App**
Wrap your app with `ProviderScope`:
```dart
void main() {
  runApp(const ProviderScope(child: MyApp()));
}
```

### 3. **Use in Screens**
Convert StatefulWidget to ConsumerStatefulWidget:
```dart
class MyScreen extends ConsumerStatefulWidget {
  @override
  ConsumerState<MyScreen> createState() => _MyScreenState();
}
```

### 4. **Add Notification Overlay**
Wrap your content with the notification overlay:
```dart
ItemsNotificationOverlay(
  scrollController: _scrollController,
  child: YourContent(),
)
```

## 🔒 Permissions

### **Android** (`android/app/src/main/AndroidManifest.xml`)
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
```

### **iOS** (`ios/Runner/Info.plist`)
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs location access to show nearby items</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>This app needs background location to update items automatically</string>
```

## 🎨 UI Components

### **New Items Notification**
- Pill-shaped notification at top of screen
- Gradient background with app theme colors
- Smooth slide-in animation
- Tap to scroll to top functionality

### **Background Refresh Indicator**
- Small loading indicator during background refresh
- Non-intrusive design
- Theme-aware colors

### **Items Grid**
- Maintains existing design
- Hero animations preserved
- Fade transitions for navigation

## 🔄 State Flow

1. **App Launch**
   - LocationService initializes
   - Cached location loaded
   - Items provider starts listening

2. **Location Update**
   - Background service detects movement
   - New location triggers item refresh
   - New items merged with existing list

3. **New Items Found**
   - Items added to top of list
   - Notification shown to user
   - List maintains scroll position

4. **User Interaction**
   - Tap notification → scroll to top
   - Pull to refresh → manual update
   - Navigate to item → Hero animation

## 🐛 Troubleshooting

### **Location Not Updating**
- Check permissions in device settings
- Ensure location services enabled
- Verify background app refresh allowed

### **Items Not Refreshing**
- Check network connectivity
- Verify Firestore rules
- Check console for error messages

### **Notification Not Showing**
- Ensure new items are actually found
- Check if user is at top of list
- Verify notification hasn't auto-hidden

## 📊 Performance

### **Memory Usage**
- Efficient list merging (no full replacement)
- Cached location data
- Optimized background timers

### **Battery Impact**
- Configurable update frequency
- Smart motion detection
- Background optimization

### **Network Usage**
- Cached data prioritized
- Background refresh only when needed
- Efficient Firestore queries

## 🔮 Future Enhancements

- Push notifications for new items
- Geofencing for specific areas
- Machine learning for item preferences
- Offline mode with sync
- Advanced filtering options

This system provides a robust, user-friendly experience that keeps the items list fresh without disrupting the user's interaction with the app.
